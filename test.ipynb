{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7576c039", "metadata": {}, "outputs": [], "source": ["# index.insert_nodes(nodes)\n", "# in batch of 50\n", "from llama_index.core import VectorStoreIndex,StorageContext,vector_stores\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "from qdrant_client import QdrantClient\n", "import os\n", "from llama_index.vector_stores.qdrant import QdrantVectorStore\n", "from llama_index.core.schema import TextNode\n", "import pickle\n", "client = QdrantClient(host=\"localhost\", port=6333)\n", "\n", "    # choose embedding model\n", "embed_model = OpenAIEmbedding(\n", "    model_name=\"text-embedding-3-large\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\"),\n", "    dimensions=2048\n", ")\n", "datas = pickle.load(open(\"jina_embeddings.pkl\", \"rb\"))\n"]}, {"cell_type": "code", "execution_count": null, "id": "aeb4651a", "metadata": {}, "outputs": [], "source": ["import json\n", "with"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}