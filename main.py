# index.insert_nodes(nodes)
# in batch of 50
from llama_index.core import VectorStoreIndex,StorageContext,vector_stores
from llama_index.embeddings.openai import OpenAIEmbedding
from qdrant_client import QdrantClient
import os
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core.schema import TextNode
import pickle
client = QdrantClient(host="localhost", port=6333)
from dotenv import load_dotenv
load_dotenv()
    # choose embedding model
embed_model = OpenAIEmbedding(
    model_name="text-embedding-3-large",
    api_key=os.getenv("OPENAI_API_KEY"),
    dimensions=2048
)
datas = pickle.load(open("jina_embeddings.pkl", "rb"))
len(datas)
import numpy as np
nodes_=[]
for node in datas:
    # Convert to NumPy array with float32 type
    node_metadata = node.get("metadata").copy()
    node_metadata.pop("text")
    embedding = np.array(node.get("embedding"), dtype=np.float32)

    # Optional: sanity check the dimension
    if embedding.shape[0] != 2048:
        raise ValueError(f"Node embedding has wrong dimension: {embedding.shape[0]}")
    nodes_.append(TextNode(text=node.get("text"), embedding=embedding, metadata=node.get("metadata")))

vector_store = QdrantVectorStore(client=client, collection_name="jina_embeddings_articles")
index = VectorStoreIndex.from_vector_store(vector_store,embed_model=None)

# Batch processing to avoid timeouts
import time
batch_size = 50  # Process 50 vectors at a time
total_nodes = len(nodes_)

index.insert_nodes(nodes=nodes_)

# Persist the storage context
try:
    index.storage_context.persist()
    print("💾 Storage context persisted successfully!")
except Exception as e:
    print(f"⚠️ Warning: Could not persist storage context: {e}")

print("✅ Upload process finished!")