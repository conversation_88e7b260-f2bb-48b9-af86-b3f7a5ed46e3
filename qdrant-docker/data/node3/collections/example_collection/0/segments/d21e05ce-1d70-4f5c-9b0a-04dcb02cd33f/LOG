2025/08/28-06:43:08.540827 81 RocksDB version: 8.1.1
2025/08/28-06:43:08.540860 81 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.540865 81 DB SUMMARY
2025/08/28-06:43:08.540868 81 DB Session ID:  24UZTVC9C92XPIYHBVQZ
2025/08/28-06:43:08.540990 81 SST files in ./storage/collections/example_collection/0/segments/d21e05ce-1d70-4f5c-9b0a-04dcb02cd33f dir, Total Num: 0, files: 
2025/08/28-06:43:08.540999 81 Write Ahead Log file in ./storage/collections/example_collection/0/segments/d21e05ce-1d70-4f5c-9b0a-04dcb02cd33f: 
2025/08/28-06:43:08.541003 81                         Options.error_if_exists: 0
2025/08/28-06:43:08.541007 81                       Options.create_if_missing: 1
2025/08/28-06:43:08.541010 81                         Options.paranoid_checks: 1
2025/08/28-06:43:08.541014 81             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.541024 81                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.541027 81        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.541034 81                                     Options.env: 0x565309db29c0
2025/08/28-06:43:08.541039 81                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.541043 81                                Options.info_log: 0x7f20c8010540
2025/08/28-06:43:08.541046 81                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.541050 81                              Options.statistics: (nil)
2025/08/28-06:43:08.541054 81                               Options.use_fsync: 0
2025/08/28-06:43:08.541059 81                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.541063 81                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.541068 81                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.541073 81                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.541080 81                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.541083 81                         Options.allow_fallocate: 1
2025/08/28-06:43:08.541089 81                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.541093 81                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.541096 81                        Options.use_direct_reads: 0
2025/08/28-06:43:08.541100 81                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.541103 81          Options.create_missing_column_families: 1
2025/08/28-06:43:08.541107 81                              Options.db_log_dir: 
2025/08/28-06:43:08.541112 81                                 Options.wal_dir: 
2025/08/28-06:43:08.541117 81                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.541121 81                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.541124 81                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.541128 81                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.541132 81             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.541136 81                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.541139 81                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.541142 81                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.541146 81                    Options.write_buffer_manager: 0x7f20c800fae0
2025/08/28-06:43:08.541155 81         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.541159 81           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.541162 81                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.541166 81                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.541170 81     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.541176 81                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.541182 81                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.541186 81                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.541189 81                  Options.unordered_write: 0
2025/08/28-06:43:08.541197 81         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.541203 81      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.541206 81             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.541212 81            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.541216 81                               Options.row_cache: None
2025/08/28-06:43:08.541220 81                              Options.wal_filter: None
2025/08/28-06:43:08.541223 81             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.541227 81             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.541233 81             Options.two_write_queues: 0
2025/08/28-06:43:08.541238 81             Options.manual_wal_flush: 0
2025/08/28-06:43:08.541243 81             Options.wal_compression: 0
2025/08/28-06:43:08.541247 81             Options.atomic_flush: 0
2025/08/28-06:43:08.541251 81             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.541255 81                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.541259 81                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.541263 81                 Options.log_readahead_size: 0
2025/08/28-06:43:08.541269 81                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.541273 81                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.541276 81                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.541280 81            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.541283 81             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.541288 81             Options.db_host_id: __hostname__
2025/08/28-06:43:08.541295 81             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.541299 81             Options.max_background_jobs: 2
2025/08/28-06:43:08.541303 81             Options.max_background_compactions: -1
2025/08/28-06:43:08.541307 81             Options.max_subcompactions: 1
2025/08/28-06:43:08.541312 81             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.541318 81           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.541322 81             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.541326 81             Options.max_total_wal_size: 0
2025/08/28-06:43:08.541330 81             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.541334 81                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.541338 81                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.541342 81                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.541347 81                          Options.max_open_files: 256
2025/08/28-06:43:08.541351 81                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.541355 81                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.541360 81                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.541364 81       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.541368 81                  Options.max_background_flushes: -1
2025/08/28-06:43:08.541371 81 Compression algorithms supported:
2025/08/28-06:43:08.541376 81 	kZSTD supported: 0
2025/08/28-06:43:08.541378 81 	kXpressCompression supported: 0
2025/08/28-06:43:08.541381 81 	kBZip2Compression supported: 0
2025/08/28-06:43:08.541384 81 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.541388 81 	kLZ4Compression supported: 0
2025/08/28-06:43:08.541392 81 	kZlibCompression supported: 0
2025/08/28-06:43:08.541395 81 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.541398 81 	kSnappyCompression supported: 1
2025/08/28-06:43:08.541404 81 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.541408 81 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.554945 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.554956 81           Options.merge_operator: None
2025/08/28-06:43:08.554960 81        Options.compaction_filter: None
2025/08/28-06:43:08.554966 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.554969 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.554972 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.554974 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.555048 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c800d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c800d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.555052 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.555057 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.555061 81          Options.compression: Snappy
2025/08/28-06:43:08.555064 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.555067 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.555070 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.555073 81             Options.num_levels: 7
2025/08/28-06:43:08.555075 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.555078 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.555081 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.555084 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.555087 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.555089 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.555092 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555095 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555098 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555100 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.555103 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555106 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555110 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.555113 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.555124 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.555127 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555130 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555136 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555139 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555141 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.555146 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555149 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.555152 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.555158 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.555161 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.555164 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.555166 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.555169 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.555175 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.555179 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.555206 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.555209 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.555211 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.555214 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.555216 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.555219 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.555221 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.555224 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.555227 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.555229 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.555232 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.555238 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.555241 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.555246 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.555254 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.555256 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.555259 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.555262 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.555291 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.555293 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.555297 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.555300 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.555309 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.555321 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.555324 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.555326 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.555330 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.555352 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.555373 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.555376 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.555394 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.555396 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.555399 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.555401 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.555416 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.555418 81                               Options.ttl: 2592000
2025/08/28-06:43:08.555421 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.555424 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.555427 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.555429 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.555432 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.555438 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.555441 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.555447 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.555451 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.555465 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.555475 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.555478 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.555481 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.590588 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.590599 81           Options.merge_operator: None
2025/08/28-06:43:08.590602 81        Options.compaction_filter: None
2025/08/28-06:43:08.590606 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.590609 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.590612 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.590615 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.590698 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c8003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c8003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.590754 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.590910 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.590915 81          Options.compression: Snappy
2025/08/28-06:43:08.590919 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.590922 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.590924 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.590929 81             Options.num_levels: 7
2025/08/28-06:43:08.590932 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.590935 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.590938 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.590941 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.590949 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.590952 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.590955 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590958 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590961 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590964 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.590967 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.590970 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590972 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.590975 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.590980 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.590985 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590987 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590994 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590997 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.591000 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.591003 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.591006 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.591013 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.591016 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.591019 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.591022 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.591025 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.591037 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.591044 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.591048 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.591051 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.591054 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.591057 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.591061 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.591064 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.591069 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.591072 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.591075 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.591078 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.591084 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.591087 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.591092 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.591095 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.591098 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.591102 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.591105 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.591108 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.591111 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.591114 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.591117 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.591121 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.591124 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.591126 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.591141 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.591144 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.591147 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.591160 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.591168 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.591175 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.591178 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.591184 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.591189 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.591192 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.591195 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.591197 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.591200 81                               Options.ttl: 2592000
2025/08/28-06:43:08.591203 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.591205 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.591208 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.591211 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.591214 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.591216 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.591221 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.591224 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.591228 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.591232 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.591237 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.591240 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.591246 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.603382 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.603391 81           Options.merge_operator: None
2025/08/28-06:43:08.603394 81        Options.compaction_filter: None
2025/08/28-06:43:08.603397 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.603400 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.603404 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.603408 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.603459 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c8005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c80061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.603464 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.603468 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.603472 81          Options.compression: Snappy
2025/08/28-06:43:08.603475 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.603483 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.603486 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.603490 81             Options.num_levels: 7
2025/08/28-06:43:08.603494 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.603498 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.603501 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.603505 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.603509 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.603512 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.603515 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.603519 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.603522 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.603526 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.603530 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.603534 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.603537 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.603540 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.603544 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.603547 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.603550 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.603553 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.603556 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.603560 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.603563 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.603567 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.603570 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.603574 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.603577 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.603580 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.603583 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.603586 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.603592 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.603597 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.603600 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.603604 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.603607 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.603610 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.603613 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.603616 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.603620 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.603623 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.603626 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.603629 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.603634 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.603637 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.603641 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.603645 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.603648 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.603652 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.603655 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.603658 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.603661 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.603665 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.603669 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.603672 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.603676 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.603685 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.603688 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.603691 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.603695 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.603699 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.603715 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.603718 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.603720 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.603723 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.603726 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.603729 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.603732 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.603735 81                               Options.ttl: 2592000
2025/08/28-06:43:08.603737 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.603742 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.603747 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.603750 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.603753 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.603756 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.603760 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.603763 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.603767 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.603771 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.603774 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.603777 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.603781 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.619804 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.619813 81           Options.merge_operator: None
2025/08/28-06:43:08.619817 81        Options.compaction_filter: None
2025/08/28-06:43:08.619821 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.619824 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.619828 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.619832 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.619879 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c8008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c8008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.619885 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.619889 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.619893 81          Options.compression: Snappy
2025/08/28-06:43:08.619897 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.619901 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.619904 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.619908 81             Options.num_levels: 7
2025/08/28-06:43:08.619912 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.619915 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.619919 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.619922 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.619926 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.619929 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.619933 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619936 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619940 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619944 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.619947 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619951 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619954 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.619958 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.619962 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.619965 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619969 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619972 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619975 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619979 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.619982 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619985 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.619989 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.619992 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.619995 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.619999 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.620002 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.620006 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.620010 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.620015 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.620018 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.620021 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.620024 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.620028 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.620031 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.620035 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.620038 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.620041 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.620045 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.620048 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.620052 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.620055 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.620058 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.620062 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.620066 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.620069 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.620072 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.620075 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.620079 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.620082 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.620086 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.620089 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.620092 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.620102 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.620105 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.620109 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.620113 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.620117 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.620120 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.620124 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.620127 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.620131 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.620134 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.620137 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.620141 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.620144 81                               Options.ttl: 2592000
2025/08/28-06:43:08.620147 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.620151 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.620154 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.620158 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.620161 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.620165 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.620169 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.620172 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.620176 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.620181 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.620185 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.620188 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.620192 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.633469 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.633478 81           Options.merge_operator: None
2025/08/28-06:43:08.633481 81        Options.compaction_filter: None
2025/08/28-06:43:08.633484 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.633486 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.633489 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.633491 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.633531 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c800ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c800aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.633536 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.633539 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.633542 81          Options.compression: Snappy
2025/08/28-06:43:08.633545 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.633548 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.633551 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.633553 81             Options.num_levels: 7
2025/08/28-06:43:08.633556 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.633559 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.633562 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.633564 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.633568 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.633570 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.633573 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.633576 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.633578 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.633581 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.633584 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.633587 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.633589 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.633592 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.633595 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.633598 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.633600 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.633603 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.633606 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.633608 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.633611 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.633614 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.633617 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.633619 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.633622 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.633625 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.633627 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.633630 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.633634 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.633638 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.633640 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.633643 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.633645 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.633648 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.633651 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.633653 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.633656 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.633658 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.633661 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.633664 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.633666 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.633669 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.633672 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.633675 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.633678 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.633681 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.633683 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.633686 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.633688 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.633691 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.633694 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.633697 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.633699 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.633723 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.633726 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.633728 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.633731 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.633735 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.633737 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.633740 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.633742 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.633745 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.633747 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.633750 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.633752 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.633755 81                               Options.ttl: 2592000
2025/08/28-06:43:08.633757 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.633760 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.633762 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.633765 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.633767 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.633770 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.633773 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.633775 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.633778 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.633782 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.633785 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.633788 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.633790 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.651458 81 DB pointer 0x7f20c8014700
