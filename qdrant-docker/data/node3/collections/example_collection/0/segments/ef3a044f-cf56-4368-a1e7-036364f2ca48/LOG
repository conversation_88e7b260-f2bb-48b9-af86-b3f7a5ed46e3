2025/08/28-06:43:08.540222 76 RocksDB version: 8.1.1
2025/08/28-06:43:08.540257 76 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.540261 76 DB SUMMARY
2025/08/28-06:43:08.540264 76 DB Session ID:  24UZTVC9C92XPIYHBVQX
2025/08/28-06:43:08.540416 76 SST files in ./storage/collections/example_collection/0/segments/ef3a044f-cf56-4368-a1e7-036364f2ca48 dir, Total Num: 0, files: 
2025/08/28-06:43:08.540425 76 Write Ahead Log file in ./storage/collections/example_collection/0/segments/ef3a044f-cf56-4368-a1e7-036364f2ca48: 
2025/08/28-06:43:08.540430 76                         Options.error_if_exists: 0
2025/08/28-06:43:08.540434 76                       Options.create_if_missing: 1
2025/08/28-06:43:08.540441 76                         Options.paranoid_checks: 1
2025/08/28-06:43:08.540444 76             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.540458 76                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.540465 76        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.540468 76                                     Options.env: 0x565309db29c0
2025/08/28-06:43:08.540472 76                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.540475 76                                Options.info_log: 0x7f20e0010540
2025/08/28-06:43:08.540478 76                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.540481 76                              Options.statistics: (nil)
2025/08/28-06:43:08.540484 76                               Options.use_fsync: 0
2025/08/28-06:43:08.540487 76                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.540491 76                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.540493 76                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.540496 76                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.540499 76                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.540502 76                         Options.allow_fallocate: 1
2025/08/28-06:43:08.540505 76                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.540508 76                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.540510 76                        Options.use_direct_reads: 0
2025/08/28-06:43:08.540513 76                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.540516 76          Options.create_missing_column_families: 1
2025/08/28-06:43:08.540518 76                              Options.db_log_dir: 
2025/08/28-06:43:08.540521 76                                 Options.wal_dir: 
2025/08/28-06:43:08.540523 76                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.540526 76                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.540528 76                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.540531 76                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.540534 76             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.540536 76                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.540539 76                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.540541 76                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.540544 76                    Options.write_buffer_manager: 0x7f20e000fae0
2025/08/28-06:43:08.540551 76         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.540555 76           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.540558 76                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.540561 76                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.540564 76     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.540567 76                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.540569 76                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.540572 76                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.540574 76                  Options.unordered_write: 0
2025/08/28-06:43:08.540582 76         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.540585 76      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.540588 76             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.540590 76            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.540593 76                               Options.row_cache: None
2025/08/28-06:43:08.540595 76                              Options.wal_filter: None
2025/08/28-06:43:08.540598 76             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.540600 76             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.540603 76             Options.two_write_queues: 0
2025/08/28-06:43:08.540605 76             Options.manual_wal_flush: 0
2025/08/28-06:43:08.540608 76             Options.wal_compression: 0
2025/08/28-06:43:08.540610 76             Options.atomic_flush: 0
2025/08/28-06:43:08.540613 76             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.540615 76                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.540618 76                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.540734 76                 Options.log_readahead_size: 0
2025/08/28-06:43:08.540750 76                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.540755 76                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.540759 76                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.540762 76            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.540766 76             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.540770 76             Options.db_host_id: __hostname__
2025/08/28-06:43:08.540780 76             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.540784 76             Options.max_background_jobs: 2
2025/08/28-06:43:08.540788 76             Options.max_background_compactions: -1
2025/08/28-06:43:08.540791 76             Options.max_subcompactions: 1
2025/08/28-06:43:08.540795 76             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.540799 76           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.540802 76             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.540806 76             Options.max_total_wal_size: 0
2025/08/28-06:43:08.540809 76             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.540813 76                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.540817 76                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.540820 76                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.540824 76                          Options.max_open_files: 256
2025/08/28-06:43:08.540827 76                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.540831 76                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.540834 76                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.540837 76       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.540841 76                  Options.max_background_flushes: -1
2025/08/28-06:43:08.540845 76 Compression algorithms supported:
2025/08/28-06:43:08.540849 76 	kZSTD supported: 0
2025/08/28-06:43:08.540853 76 	kXpressCompression supported: 0
2025/08/28-06:43:08.540857 76 	kBZip2Compression supported: 0
2025/08/28-06:43:08.540861 76 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.540866 76 	kLZ4Compression supported: 0
2025/08/28-06:43:08.540869 76 	kZlibCompression supported: 0
2025/08/28-06:43:08.540873 76 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.540877 76 	kSnappyCompression supported: 1
2025/08/28-06:43:08.540883 76 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.540887 76 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.556571 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.556581 76           Options.merge_operator: None
2025/08/28-06:43:08.556586 76        Options.compaction_filter: None
2025/08/28-06:43:08.556595 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.556598 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.556602 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.556607 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.556685 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20e000d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20e000d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.556858 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.556864 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.556868 76          Options.compression: Snappy
2025/08/28-06:43:08.556873 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.556877 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.556881 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.556885 76             Options.num_levels: 7
2025/08/28-06:43:08.556889 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.556893 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.556897 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.556901 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.556905 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.556910 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.556913 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556918 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556923 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556928 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.556932 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556936 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556940 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.556944 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.556953 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.556957 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556961 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556966 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556969 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556972 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.556976 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556980 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.556984 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.556992 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.556996 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.556999 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.557003 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.557007 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.557014 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.557020 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.557025 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.557029 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.557033 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.557038 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.557043 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.557047 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.557051 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.557055 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.557060 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.557064 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.557068 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.557072 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.557077 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.557082 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.557091 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.557096 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.557100 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.557103 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.557107 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.557111 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.557115 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.557119 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.557123 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.557135 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.557139 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.557143 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.557148 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.557152 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.557157 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.557160 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.557163 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.557166 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.557171 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.557174 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.557178 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.557183 76                               Options.ttl: 2592000
2025/08/28-06:43:08.557187 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.557192 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.557196 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.557200 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.557205 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.557213 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.557218 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.557227 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.557233 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.557238 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.557243 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.557247 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.557251 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.584249 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.584259 76           Options.merge_operator: None
2025/08/28-06:43:08.584263 76        Options.compaction_filter: None
2025/08/28-06:43:08.584266 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.584269 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.584272 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.584275 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.584348 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20e0003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20e0003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.584353 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.584357 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.584361 76          Options.compression: Snappy
2025/08/28-06:43:08.584364 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.584367 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.584370 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.584373 76             Options.num_levels: 7
2025/08/28-06:43:08.584377 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.584380 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.584383 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.584386 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.584390 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.584394 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.584397 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584400 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584404 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584407 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.584410 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584413 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584416 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.584419 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.584423 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.584426 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584429 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584432 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584436 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584439 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.584442 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584445 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.584449 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.584454 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.584457 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.584460 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.584464 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.584467 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.584473 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.584477 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.584484 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.584488 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.584491 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.584494 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.584497 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.584500 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.584505 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.584508 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.584511 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.584514 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.584518 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.584521 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.584524 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.584529 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.584533 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.584536 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.584539 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.584543 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.584546 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.584549 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.584553 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.584556 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.584561 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.584573 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.584577 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.584580 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.584584 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.584588 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.584592 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.584599 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.584602 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.584606 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.584609 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.584613 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.584616 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.584620 76                               Options.ttl: 2592000
2025/08/28-06:43:08.584623 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.584626 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.584630 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.584633 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.584636 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.584640 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.584643 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.584647 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.584651 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.584655 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.584659 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.584663 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.584667 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.598920 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.598928 76           Options.merge_operator: None
2025/08/28-06:43:08.598932 76        Options.compaction_filter: None
2025/08/28-06:43:08.598935 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.598938 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.598940 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.598942 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.598982 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20e0005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20e00061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.598986 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.598989 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.598992 76          Options.compression: Snappy
2025/08/28-06:43:08.598994 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.598996 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.598998 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.599000 76             Options.num_levels: 7
2025/08/28-06:43:08.599003 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.599005 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.599009 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.599012 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.599014 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.599017 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.599019 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599021 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599023 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599026 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.599028 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599030 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599032 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599034 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599036 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599038 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599040 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599044 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599046 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599048 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599050 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599052 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599054 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599057 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599059 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599061 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599063 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599065 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599069 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599072 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599075 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599077 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599079 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599081 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599083 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599085 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599087 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599089 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599092 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599094 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599097 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599100 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599103 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599107 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599110 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599112 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599114 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599118 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599121 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599124 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599127 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599130 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599133 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599144 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599148 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599151 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599155 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599160 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599163 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599166 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.599170 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599172 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599174 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599176 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599178 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599180 76                               Options.ttl: 2592000
2025/08/28-06:43:08.599182 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599186 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599188 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599190 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.599194 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.599198 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599202 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599205 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599209 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599213 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599217 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599220 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599224 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.615248 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.615256 76           Options.merge_operator: None
2025/08/28-06:43:08.615259 76        Options.compaction_filter: None
2025/08/28-06:43:08.615262 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.615265 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.615268 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.615272 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.615325 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20e0008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20e0008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.615330 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.615334 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.615337 76          Options.compression: Snappy
2025/08/28-06:43:08.615339 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.615342 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.615347 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.615350 76             Options.num_levels: 7
2025/08/28-06:43:08.615353 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.615357 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.615360 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.615363 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.615366 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.615370 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.615373 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615376 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615378 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615381 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.615383 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615386 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615389 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.615392 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.615395 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.615399 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615402 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615405 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615408 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615413 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.615416 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615420 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.615423 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.615427 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.615448 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.615449 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.615451 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.615452 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.615454 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.615456 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.615457 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.615459 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.615461 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.615462 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.615463 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.615464 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.615466 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.615467 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.615468 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.615470 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.615471 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.615472 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.615474 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.615476 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.615477 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.615479 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.615480 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.615481 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.615483 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.615483 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.615484 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.615486 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.615487 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.615490 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.615491 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.615492 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.615494 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.615495 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.615497 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.615498 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.615499 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.615500 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.615501 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.615503 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.615504 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.615506 76                               Options.ttl: 2592000
2025/08/28-06:43:08.615507 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.615508 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.615509 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.615511 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.615512 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.615514 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.615516 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.615517 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.615519 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.615520 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.615522 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.615523 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.615525 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.631886 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.631895 76           Options.merge_operator: None
2025/08/28-06:43:08.631898 76        Options.compaction_filter: None
2025/08/28-06:43:08.631901 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.631904 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.631906 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.631909 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.631954 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20e000ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20e000aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.631958 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.631961 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.631964 76          Options.compression: Snappy
2025/08/28-06:43:08.631967 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.631970 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.631973 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.631976 76             Options.num_levels: 7
2025/08/28-06:43:08.631979 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.631982 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.631985 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.631987 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.631990 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.631993 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.632023 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.632026 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.632029 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.632032 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.632035 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.632038 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.632041 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.632044 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.632046 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.632049 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.632052 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.632055 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.632058 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.632061 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.632063 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.632066 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.632069 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.632071 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.632074 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.632077 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.632080 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.632082 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.632087 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.632090 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.632093 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.632096 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.632098 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.632101 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.632103 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.632106 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.632109 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.632111 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.632114 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.632117 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.632119 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.632122 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.632125 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.632128 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.632131 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.632134 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.632137 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.632139 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.632142 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.632144 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.632147 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.632150 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.632153 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.632161 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.632164 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.632167 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.632170 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.632173 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.632176 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.632179 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.632181 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.632184 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.632186 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.632188 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.632190 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.632193 76                               Options.ttl: 2592000
2025/08/28-06:43:08.632196 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.632198 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.632201 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.632203 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.632206 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.632208 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.632211 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.632214 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.632218 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.632221 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.632225 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.632227 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.632230 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.654896 76 DB pointer 0x7f20e0014700
