2025/08/28-06:43:08.539153 78 RocksDB version: 8.1.1
2025/08/28-06:43:08.539309 78 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.539333 78 DB SUMMARY
2025/08/28-06:43:08.539337 78 DB Session ID:  24UZTVC9C92XPIYHBVR1
2025/08/28-06:43:08.539680 78 SST files in ./storage/collections/example_collection/0/segments/9ed89be5-43d4-4987-b392-1c228fdd9e1f dir, Total Num: 0, files: 
2025/08/28-06:43:08.539691 78 Write Ahead Log file in ./storage/collections/example_collection/0/segments/9ed89be5-43d4-4987-b392-1c228fdd9e1f: 
2025/08/28-06:43:08.539696 78                         Options.error_if_exists: 0
2025/08/28-06:43:08.539699 78                       Options.create_if_missing: 1
2025/08/28-06:43:08.539717 78                         Options.paranoid_checks: 1
2025/08/28-06:43:08.539721 78             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.539723 78                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.539725 78        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.539728 78                                     Options.env: 0x565309db29c0
2025/08/28-06:43:08.539731 78                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.539734 78                                Options.info_log: 0x7f20d8010540
2025/08/28-06:43:08.539736 78                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.539739 78                              Options.statistics: (nil)
2025/08/28-06:43:08.539741 78                               Options.use_fsync: 0
2025/08/28-06:43:08.539744 78                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.539747 78                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.539751 78                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.539753 78                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.539757 78                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.539759 78                         Options.allow_fallocate: 1
2025/08/28-06:43:08.539762 78                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.539764 78                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.539767 78                        Options.use_direct_reads: 0
2025/08/28-06:43:08.539769 78                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.539771 78          Options.create_missing_column_families: 1
2025/08/28-06:43:08.539773 78                              Options.db_log_dir: 
2025/08/28-06:43:08.539780 78                                 Options.wal_dir: 
2025/08/28-06:43:08.539783 78                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.539785 78                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.539787 78                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.539789 78                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.539792 78             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.539794 78                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.539796 78                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.539798 78                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.539802 78                    Options.write_buffer_manager: 0x7f20d800fae0
2025/08/28-06:43:08.539805 78         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.539807 78           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.539809 78                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.539811 78                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.539815 78     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.539817 78                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.539819 78                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.539821 78                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.539824 78                  Options.unordered_write: 0
2025/08/28-06:43:08.539829 78         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.539833 78      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.539835 78             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.539837 78            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.539839 78                               Options.row_cache: None
2025/08/28-06:43:08.539842 78                              Options.wal_filter: None
2025/08/28-06:43:08.539847 78             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.539849 78             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.539851 78             Options.two_write_queues: 0
2025/08/28-06:43:08.539853 78             Options.manual_wal_flush: 0
2025/08/28-06:43:08.539855 78             Options.wal_compression: 0
2025/08/28-06:43:08.539857 78             Options.atomic_flush: 0
2025/08/28-06:43:08.539859 78             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.539864 78                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.539866 78                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.539868 78                 Options.log_readahead_size: 0
2025/08/28-06:43:08.539871 78                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.539873 78                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.539875 78                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.539877 78            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.539881 78             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.539884 78             Options.db_host_id: __hostname__
2025/08/28-06:43:08.539886 78             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.539890 78             Options.max_background_jobs: 2
2025/08/28-06:43:08.539895 78             Options.max_background_compactions: -1
2025/08/28-06:43:08.539899 78             Options.max_subcompactions: 1
2025/08/28-06:43:08.539901 78             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.539903 78           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.539906 78             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.539908 78             Options.max_total_wal_size: 0
2025/08/28-06:43:08.539910 78             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.539913 78                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.539915 78                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.539917 78                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.539919 78                          Options.max_open_files: 256
2025/08/28-06:43:08.539934 78                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.539936 78                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.539938 78                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.539941 78       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.539943 78                  Options.max_background_flushes: -1
2025/08/28-06:43:08.539957 78 Compression algorithms supported:
2025/08/28-06:43:08.539960 78 	kZSTD supported: 0
2025/08/28-06:43:08.539963 78 	kXpressCompression supported: 0
2025/08/28-06:43:08.539966 78 	kBZip2Compression supported: 0
2025/08/28-06:43:08.539968 78 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.539971 78 	kLZ4Compression supported: 0
2025/08/28-06:43:08.539976 78 	kZlibCompression supported: 0
2025/08/28-06:43:08.539978 78 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.539980 78 	kSnappyCompression supported: 1
2025/08/28-06:43:08.539984 78 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.539987 78 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.554923 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.554935 78           Options.merge_operator: None
2025/08/28-06:43:08.554938 78        Options.compaction_filter: None
2025/08/28-06:43:08.554944 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.554947 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.554953 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.554955 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.555023 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d800d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d800d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.555031 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.555034 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.555038 78          Options.compression: Snappy
2025/08/28-06:43:08.555040 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.555043 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.555050 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.555053 78             Options.num_levels: 7
2025/08/28-06:43:08.555057 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.555060 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.555062 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.555065 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.555068 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.555073 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.555075 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555078 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555081 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555087 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.555090 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555093 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555096 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.555099 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.555101 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.555104 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555107 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555111 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555113 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555120 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.555123 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555127 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.555129 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.555138 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.555141 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.555146 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.555148 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.555151 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.555156 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.555161 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.555163 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.555166 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.555172 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.555175 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.555178 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.555180 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.555183 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.555185 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.555188 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.555194 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.555197 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.555205 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.555208 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.555212 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.555215 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.555220 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.555223 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.555225 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.555228 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.555231 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.555234 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.555236 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.555239 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.555250 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.555253 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.555256 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.555262 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.555267 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.555269 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.555272 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.555274 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.555277 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.555279 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.555310 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.555313 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.555315 78                               Options.ttl: 2592000
2025/08/28-06:43:08.555320 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.555323 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.555329 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.555344 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.555358 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.555364 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.555367 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.555370 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.555373 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.555376 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.555381 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.555383 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.555386 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.590591 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.590600 78           Options.merge_operator: None
2025/08/28-06:43:08.590604 78        Options.compaction_filter: None
2025/08/28-06:43:08.590607 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.590610 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.590614 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.590617 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.590695 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d8003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d8003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.590738 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.590895 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.590950 78          Options.compression: Snappy
2025/08/28-06:43:08.590954 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.590957 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.590959 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.590962 78             Options.num_levels: 7
2025/08/28-06:43:08.590965 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.590968 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.590971 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.590974 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.590977 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.590980 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.590986 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590989 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590992 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590996 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.590999 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.591002 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.591005 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.591011 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.591137 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.591145 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.591149 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.591152 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.591154 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.591157 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.591159 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.591162 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.591164 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.591166 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.591169 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.591174 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.591177 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.591193 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.591198 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.591201 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.591204 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.591206 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.591208 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.591210 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.591212 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.591214 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.591216 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.591221 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.591223 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.591225 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.591227 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.591229 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.591231 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.591234 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.591239 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.591241 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.591243 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.591245 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.591248 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.591250 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.591252 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.591255 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.591257 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.591265 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.591267 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.591269 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.591275 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.591277 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.591279 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.591281 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.591284 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.591286 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.591288 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.591290 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.591292 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.591294 78                               Options.ttl: 2592000
2025/08/28-06:43:08.591296 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.591298 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.591300 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.591302 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.591304 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.591306 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.591309 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.591311 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.591314 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.591316 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.591319 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.591321 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.591323 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.603376 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.603385 78           Options.merge_operator: None
2025/08/28-06:43:08.603388 78        Options.compaction_filter: None
2025/08/28-06:43:08.603391 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.603394 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.603398 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.603401 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.603449 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d8005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d80061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.603455 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.603458 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.603462 78          Options.compression: Snappy
2025/08/28-06:43:08.603465 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.603469 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.603472 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.603475 78             Options.num_levels: 7
2025/08/28-06:43:08.603482 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.603486 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.603490 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.603493 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.603496 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.603500 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.603503 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.603507 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.603510 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.603514 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.603517 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.603521 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.603524 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.603528 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.603531 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.603535 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.603538 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.603541 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.603545 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.603548 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.603551 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.603554 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.603558 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.603561 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.603564 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.603568 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.603571 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.603574 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.603579 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.603584 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.603587 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.603590 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.603593 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.603596 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.603600 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.603603 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.603606 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.603609 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.603613 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.603616 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.603619 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.603622 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.603625 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.603629 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.603636 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.603639 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.603643 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.603646 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.603650 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.603653 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.603657 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.603660 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.603663 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.603672 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.603677 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.603680 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.603684 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.603687 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.603690 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.603693 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.603697 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.603718 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.603722 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.603724 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.603727 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.603730 78                               Options.ttl: 2592000
2025/08/28-06:43:08.603733 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.603737 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.603742 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.603747 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.603751 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.603754 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.603758 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.603761 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.603765 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.603770 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.603775 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.603778 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.603781 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.619064 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.619075 78           Options.merge_operator: None
2025/08/28-06:43:08.619080 78        Options.compaction_filter: None
2025/08/28-06:43:08.619086 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.619090 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.619094 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.619098 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.619154 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d8008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d8008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.619161 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.619165 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.619169 78          Options.compression: Snappy
2025/08/28-06:43:08.619173 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.619177 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.619181 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.619184 78             Options.num_levels: 7
2025/08/28-06:43:08.619189 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.619192 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.619196 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.619200 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.619204 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.619208 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.619212 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619216 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619219 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619223 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.619227 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619231 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619235 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.619239 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.619243 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.619247 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619252 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619256 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619259 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619263 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.619267 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619271 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.619274 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.619278 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.619282 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.619286 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.619290 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.619294 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.619300 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.619306 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.619309 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.619313 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.619317 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.619321 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.619324 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.619327 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.619331 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.619335 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.619339 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.619342 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.619346 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.619350 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.619353 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.619358 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.619363 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.619367 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.619370 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.619374 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.619378 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.619382 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.619386 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.619390 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.619394 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.619405 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.619408 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.619412 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.619417 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.619421 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.619424 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.619429 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.619432 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.619435 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.619439 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.619442 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.619446 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.619450 78                               Options.ttl: 2592000
2025/08/28-06:43:08.619453 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.619457 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.619461 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.619464 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.619468 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.619471 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.619476 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.619479 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.619484 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.619488 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.619493 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.619496 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.619501 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637847 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637854 78           Options.merge_operator: None
2025/08/28-06:43:08.637857 78        Options.compaction_filter: None
2025/08/28-06:43:08.637860 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637863 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637866 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637868 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637917 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d800ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d800aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637922 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637925 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637928 78          Options.compression: Snappy
2025/08/28-06:43:08.637931 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637933 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637936 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637938 78             Options.num_levels: 7
2025/08/28-06:43:08.637941 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637944 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637947 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637949 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.637952 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.637955 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.637957 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637960 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637963 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637965 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.637968 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637970 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637973 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.637976 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.637978 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.637981 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637984 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637986 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637989 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637991 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.637993 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637996 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.637999 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.638001 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.638004 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.638007 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.638009 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.638010 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.638013 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.638015 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.638017 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.638019 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.638021 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.638023 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.638025 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.638026 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.638028 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.638030 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.638032 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.638033 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.638035 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.638037 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.638039 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.638041 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.638043 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.638045 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.638047 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.638049 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.638050 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.638052 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.638054 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638056 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638059 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638064 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638066 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638068 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638071 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638073 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638075 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638077 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.638079 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638081 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638083 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638085 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638087 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638089 78                               Options.ttl: 2592000
2025/08/28-06:43:08.638091 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638093 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638095 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638097 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.638099 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.638101 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638103 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638105 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638107 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638109 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638111 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638113 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638115 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.657253 78 DB pointer 0x7f20d8014700
