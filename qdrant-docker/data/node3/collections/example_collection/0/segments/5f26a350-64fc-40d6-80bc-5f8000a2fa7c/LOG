2025/08/28-06:43:08.539222 75 RocksDB version: 8.1.1
2025/08/28-06:43:08.539309 75 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.539324 75 DB SUMMARY
2025/08/28-06:43:08.539331 75 DB Session ID:  24UZTVC9C92XPIYHBVR3
2025/08/28-06:43:08.539670 75 SST files in ./storage/collections/example_collection/0/segments/5f26a350-64fc-40d6-80bc-5f8000a2fa7c dir, Total Num: 0, files: 
2025/08/28-06:43:08.539680 75 Write Ahead Log file in ./storage/collections/example_collection/0/segments/5f26a350-64fc-40d6-80bc-5f8000a2fa7c: 
2025/08/28-06:43:08.539684 75                         Options.error_if_exists: 0
2025/08/28-06:43:08.539688 75                       Options.create_if_missing: 1
2025/08/28-06:43:08.539693 75                         Options.paranoid_checks: 1
2025/08/28-06:43:08.539697 75             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.539714 75                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.539717 75        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.539721 75                                     Options.env: 0x565309db29c0
2025/08/28-06:43:08.539724 75                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.539727 75                                Options.info_log: 0x7f20d4010540
2025/08/28-06:43:08.539729 75                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.539732 75                              Options.statistics: (nil)
2025/08/28-06:43:08.539735 75                               Options.use_fsync: 0
2025/08/28-06:43:08.539737 75                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.539740 75                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.539742 75                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.539747 75                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.539749 75                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.539751 75                         Options.allow_fallocate: 1
2025/08/28-06:43:08.539754 75                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.539756 75                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.539758 75                        Options.use_direct_reads: 0
2025/08/28-06:43:08.539761 75                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.539763 75          Options.create_missing_column_families: 1
2025/08/28-06:43:08.539765 75                              Options.db_log_dir: 
2025/08/28-06:43:08.539767 75                                 Options.wal_dir: 
2025/08/28-06:43:08.539769 75                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.539771 75                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.539777 75                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.539779 75                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.539781 75             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.539783 75                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.539785 75                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.539787 75                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.539790 75                    Options.write_buffer_manager: 0x7f20d400fae0
2025/08/28-06:43:08.539792 75         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.539796 75           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.539800 75                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.539803 75                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.539810 75     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.539813 75                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.539815 75                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.539818 75                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.539821 75                  Options.unordered_write: 0
2025/08/28-06:43:08.539828 75         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.539831 75      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.539836 75             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.539842 75            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.539847 75                               Options.row_cache: None
2025/08/28-06:43:08.539850 75                              Options.wal_filter: None
2025/08/28-06:43:08.539852 75             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.539856 75             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.539858 75             Options.two_write_queues: 0
2025/08/28-06:43:08.539860 75             Options.manual_wal_flush: 0
2025/08/28-06:43:08.539862 75             Options.wal_compression: 0
2025/08/28-06:43:08.539864 75             Options.atomic_flush: 0
2025/08/28-06:43:08.539866 75             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.539868 75                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.539870 75                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.539873 75                 Options.log_readahead_size: 0
2025/08/28-06:43:08.539877 75                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.539882 75                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.539884 75                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.539886 75            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.539890 75             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.539895 75             Options.db_host_id: __hostname__
2025/08/28-06:43:08.539900 75             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.539904 75             Options.max_background_jobs: 2
2025/08/28-06:43:08.539908 75             Options.max_background_compactions: -1
2025/08/28-06:43:08.539910 75             Options.max_subcompactions: 1
2025/08/28-06:43:08.539912 75             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.539914 75           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.539917 75             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.539919 75             Options.max_total_wal_size: 0
2025/08/28-06:43:08.539923 75             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.539925 75                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.539927 75                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.539929 75                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.539931 75                          Options.max_open_files: 256
2025/08/28-06:43:08.539933 75                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.539935 75                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.539937 75                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.539940 75       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.539942 75                  Options.max_background_flushes: -1
2025/08/28-06:43:08.539944 75 Compression algorithms supported:
2025/08/28-06:43:08.539947 75 	kZSTD supported: 0
2025/08/28-06:43:08.539950 75 	kXpressCompression supported: 0
2025/08/28-06:43:08.539952 75 	kBZip2Compression supported: 0
2025/08/28-06:43:08.539955 75 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.539957 75 	kLZ4Compression supported: 0
2025/08/28-06:43:08.539959 75 	kZlibCompression supported: 0
2025/08/28-06:43:08.539961 75 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.539967 75 	kSnappyCompression supported: 1
2025/08/28-06:43:08.539974 75 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.539976 75 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.555042 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.555058 75           Options.merge_operator: None
2025/08/28-06:43:08.555064 75        Options.compaction_filter: None
2025/08/28-06:43:08.555073 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.555078 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.555083 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.555087 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.555179 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d400d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d400d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.555195 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.555255 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.555265 75          Options.compression: Snappy
2025/08/28-06:43:08.555276 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.555281 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.555285 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.555289 75             Options.num_levels: 7
2025/08/28-06:43:08.555294 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.555299 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.555328 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.555333 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.555342 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.555347 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.555356 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555361 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555396 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555401 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.555405 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555410 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555415 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.555420 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.555427 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.555434 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555438 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555443 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555448 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555453 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.555458 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555463 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.555467 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.555476 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.555483 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.555494 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.555499 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.555504 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.555513 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.555519 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.555524 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.555530 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.555534 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.555539 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.555544 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.555548 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.555553 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.555577 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.555587 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.555594 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.555598 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.555602 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.555606 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.555611 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.555616 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.555624 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.555629 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.555648 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.555652 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.555722 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.555728 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.555733 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.555737 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.555750 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.555754 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.555762 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.555767 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.555771 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.555776 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.555781 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.555785 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.555790 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.555794 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.555799 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.555803 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.555808 75                               Options.ttl: 2592000
2025/08/28-06:43:08.555813 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.555823 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.555828 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.555833 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.555838 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.555848 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.555853 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.555861 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.555868 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.555874 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.555880 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.555884 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.555888 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.584861 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.584869 75           Options.merge_operator: None
2025/08/28-06:43:08.584874 75        Options.compaction_filter: None
2025/08/28-06:43:08.584877 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.584880 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.584884 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.584887 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.584959 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d4003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d4003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.584965 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.584969 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.584972 75          Options.compression: Snappy
2025/08/28-06:43:08.584976 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.584979 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.584981 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.584984 75             Options.num_levels: 7
2025/08/28-06:43:08.584987 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.584990 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.584993 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.584996 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.584999 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.585002 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.585009 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.585013 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.585017 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.585020 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.585023 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.585026 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.585029 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.585032 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.585035 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.585038 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.585041 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.585043 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.585046 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.585090 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.585098 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.585102 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.585104 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.585107 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.585109 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.585112 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.585114 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.585116 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.585122 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.585126 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.585129 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.585136 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.585139 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.585141 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.585143 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.585145 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.585147 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.585150 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.585152 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.585155 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.585157 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.585160 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.585162 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.585166 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.585168 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.585171 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.585173 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.585176 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.585178 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.585180 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.585183 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.585186 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.585188 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.585196 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.585199 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.585201 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.585205 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.585207 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.585210 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.585212 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.585217 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.585220 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.585222 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.585224 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.585227 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.585229 75                               Options.ttl: 2592000
2025/08/28-06:43:08.585231 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.585233 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.585236 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.585238 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.585240 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.585243 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.585246 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.585248 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.585251 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.585255 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.585257 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.585260 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.585262 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.598844 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.598852 75           Options.merge_operator: None
2025/08/28-06:43:08.598856 75        Options.compaction_filter: None
2025/08/28-06:43:08.598858 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.598862 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.598865 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.598868 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.598988 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d4005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d40061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.598995 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.598998 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.599002 75          Options.compression: Snappy
2025/08/28-06:43:08.599005 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.599010 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.599013 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.599016 75             Options.num_levels: 7
2025/08/28-06:43:08.599020 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.599023 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.599026 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.599028 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.599032 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.599034 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.599037 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599040 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599045 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599048 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.599051 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599054 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599057 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599060 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599063 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599066 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599069 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599071 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599074 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599077 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599080 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599083 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599086 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599089 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599092 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599096 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599099 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599102 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599106 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599111 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599115 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599118 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599121 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599123 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599126 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599129 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599132 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599135 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599138 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599154 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599159 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599162 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599165 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599169 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599173 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599176 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599179 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599182 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599187 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599190 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599194 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599197 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599200 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599210 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599213 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599216 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599220 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599224 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599226 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599229 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.599232 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599234 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599236 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599239 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599241 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599244 75                               Options.ttl: 2592000
2025/08/28-06:43:08.599247 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599249 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599252 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599254 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.599257 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.599260 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599262 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599265 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599269 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599272 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599276 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599279 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599282 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.615235 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.615243 75           Options.merge_operator: None
2025/08/28-06:43:08.615248 75        Options.compaction_filter: None
2025/08/28-06:43:08.615251 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.615254 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.615257 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.615262 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.615307 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d4008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d4008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.615312 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.615316 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.615320 75          Options.compression: Snappy
2025/08/28-06:43:08.615323 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.615326 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.615329 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.615332 75             Options.num_levels: 7
2025/08/28-06:43:08.615336 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.615339 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.615342 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.615345 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.615348 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.615351 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.615357 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615360 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615363 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615366 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.615369 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615372 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615375 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.615378 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.615381 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.615384 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615387 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615390 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615393 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615396 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.615399 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615403 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.615406 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.615409 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.615412 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.615416 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.615420 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.615424 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.615444 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.615446 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.615448 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.615449 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.615450 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.615452 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.615453 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.615454 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.615456 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.615457 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.615459 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.615461 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.615463 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.615464 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.615465 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.615467 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.615469 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.615471 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.615472 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.615473 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.615475 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.615476 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.615478 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.615479 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.615480 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.615486 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.615487 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.615489 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.615490 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.615492 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.615493 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.615495 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.615496 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.615497 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.615499 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.615500 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.615501 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.615503 75                               Options.ttl: 2592000
2025/08/28-06:43:08.615504 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.615505 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.615507 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.615508 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.615509 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.615511 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.615513 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.615514 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.615516 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.615519 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.615521 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.615523 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.615524 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.632837 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.632847 75           Options.merge_operator: None
2025/08/28-06:43:08.632851 75        Options.compaction_filter: None
2025/08/28-06:43:08.632854 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.632857 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.632860 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.632863 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.632917 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d400ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d400aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.632923 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.632926 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.632930 75          Options.compression: Snappy
2025/08/28-06:43:08.632933 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.632936 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.632939 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.632942 75             Options.num_levels: 7
2025/08/28-06:43:08.632945 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.632948 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.632951 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.632954 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.632957 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.632960 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.632963 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.632966 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.632969 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.632971 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.632974 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.632977 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.632980 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.632983 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.632986 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.632988 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.632991 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.632994 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.632997 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.632999 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.633002 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.633005 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.633007 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.633010 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.633013 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.633016 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.633018 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.633022 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.633026 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.633030 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.633033 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.633036 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.633039 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.633041 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.633044 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.633046 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.633049 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.633052 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.633055 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.633057 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.633060 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.633063 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.633066 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.633070 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.633073 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.633076 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.633079 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.633081 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.633084 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.633087 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.633090 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.633093 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.633096 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.633106 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.633110 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.633112 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.633116 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.633119 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.633122 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.633125 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.633127 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.633130 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.633132 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.633135 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.633138 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.633140 75                               Options.ttl: 2592000
2025/08/28-06:43:08.633143 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.633145 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.633148 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.633151 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.633153 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.633156 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.633159 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.633162 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.633165 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.633169 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.633172 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.633175 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.633178 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.658865 75 DB pointer 0x7f20d4014700
