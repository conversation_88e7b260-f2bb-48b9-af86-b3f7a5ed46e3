2025/08/28-06:43:08.540629 80 RocksDB version: 8.1.1
2025/08/28-06:43:08.540651 80 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.540655 80 DB SUMMARY
2025/08/28-06:43:08.540659 80 DB Session ID:  24UZTVC9C92XPIYHBVQW
2025/08/28-06:43:08.540932 80 SST files in ./storage/collections/example_collection/0/segments/d016e8ea-8b43-45a0-961e-ac78147a44b0 dir, Total Num: 0, files: 
2025/08/28-06:43:08.540961 80 Write Ahead Log file in ./storage/collections/example_collection/0/segments/d016e8ea-8b43-45a0-961e-ac78147a44b0: 
2025/08/28-06:43:08.540967 80                         Options.error_if_exists: 0
2025/08/28-06:43:08.540971 80                       Options.create_if_missing: 1
2025/08/28-06:43:08.540974 80                         Options.paranoid_checks: 1
2025/08/28-06:43:08.540976 80             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.540986 80                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.540990 80        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.540993 80                                     Options.env: 0x565309db29c0
2025/08/28-06:43:08.540997 80                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.540999 80                                Options.info_log: 0x7f20c4010540
2025/08/28-06:43:08.541002 80                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.541005 80                              Options.statistics: (nil)
2025/08/28-06:43:08.541008 80                               Options.use_fsync: 0
2025/08/28-06:43:08.541011 80                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.541014 80                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.541017 80                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.541019 80                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.541022 80                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.541024 80                         Options.allow_fallocate: 1
2025/08/28-06:43:08.541027 80                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.541032 80                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.541035 80                        Options.use_direct_reads: 0
2025/08/28-06:43:08.541042 80                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.541045 80          Options.create_missing_column_families: 1
2025/08/28-06:43:08.541047 80                              Options.db_log_dir: 
2025/08/28-06:43:08.541050 80                                 Options.wal_dir: 
2025/08/28-06:43:08.541052 80                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.541059 80                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.541062 80                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.541065 80                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.541068 80             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.541073 80                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.541078 80                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.541080 80                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.541083 80                    Options.write_buffer_manager: 0x7f20c400fae0
2025/08/28-06:43:08.541094 80         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.541097 80           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.541100 80                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.541105 80                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.541109 80     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.541111 80                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.541114 80                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.541117 80                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.541119 80                  Options.unordered_write: 0
2025/08/28-06:43:08.541127 80         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.541130 80      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.541135 80             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.541138 80            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.541141 80                               Options.row_cache: None
2025/08/28-06:43:08.541143 80                              Options.wal_filter: None
2025/08/28-06:43:08.541146 80             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.541148 80             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.541151 80             Options.two_write_queues: 0
2025/08/28-06:43:08.541154 80             Options.manual_wal_flush: 0
2025/08/28-06:43:08.541156 80             Options.wal_compression: 0
2025/08/28-06:43:08.541160 80             Options.atomic_flush: 0
2025/08/28-06:43:08.541165 80             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.541168 80                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.541170 80                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.541176 80                 Options.log_readahead_size: 0
2025/08/28-06:43:08.541179 80                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.541182 80                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.541186 80                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.541189 80            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.541192 80             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.541197 80             Options.db_host_id: __hostname__
2025/08/28-06:43:08.541207 80             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.541211 80             Options.max_background_jobs: 2
2025/08/28-06:43:08.541214 80             Options.max_background_compactions: -1
2025/08/28-06:43:08.541219 80             Options.max_subcompactions: 1
2025/08/28-06:43:08.541221 80             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.541224 80           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.541227 80             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.541232 80             Options.max_total_wal_size: 0
2025/08/28-06:43:08.541235 80             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.541238 80                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.541242 80                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.541245 80                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.541247 80                          Options.max_open_files: 256
2025/08/28-06:43:08.541250 80                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.541253 80                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.541256 80                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.541259 80       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.541261 80                  Options.max_background_flushes: -1
2025/08/28-06:43:08.541264 80 Compression algorithms supported:
2025/08/28-06:43:08.541267 80 	kZSTD supported: 0
2025/08/28-06:43:08.541270 80 	kXpressCompression supported: 0
2025/08/28-06:43:08.541275 80 	kBZip2Compression supported: 0
2025/08/28-06:43:08.541278 80 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.541280 80 	kLZ4Compression supported: 0
2025/08/28-06:43:08.541283 80 	kZlibCompression supported: 0
2025/08/28-06:43:08.541286 80 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.541291 80 	kSnappyCompression supported: 1
2025/08/28-06:43:08.541295 80 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.541298 80 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.555147 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.555160 80           Options.merge_operator: None
2025/08/28-06:43:08.555165 80        Options.compaction_filter: None
2025/08/28-06:43:08.555193 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.555198 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.555202 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.555206 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.555329 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c400d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c400d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.555343 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.555347 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.555361 80          Options.compression: Snappy
2025/08/28-06:43:08.555391 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.555395 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.555401 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.555409 80             Options.num_levels: 7
2025/08/28-06:43:08.555413 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.555416 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.555420 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.555428 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.555432 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.555435 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.555441 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555445 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555449 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555459 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.555462 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555465 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555511 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.555514 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.555527 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.555531 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555535 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555538 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555541 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555544 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.555548 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555551 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.555554 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.555563 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.555567 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.555570 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.555574 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.555577 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.555584 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.555589 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.555593 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.555596 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.555600 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.555603 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.555606 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.555610 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.555613 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.555616 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.555649 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.555653 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.555659 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.555663 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.555667 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.555672 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.555683 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.555688 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.555692 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.555695 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.555698 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.555724 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.555729 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.555732 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.555736 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.555747 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.555751 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.555755 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.555764 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.555768 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.555771 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.555775 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.555778 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.555781 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.555785 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.555788 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.555791 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.555795 80                               Options.ttl: 2592000
2025/08/28-06:43:08.555798 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.555802 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.555805 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.555808 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.555812 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.555823 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.555828 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.555837 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.555843 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.555848 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.555853 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.555860 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.555864 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.584298 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.584308 80           Options.merge_operator: None
2025/08/28-06:43:08.584311 80        Options.compaction_filter: None
2025/08/28-06:43:08.584314 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.584316 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.584319 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.584322 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.584397 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c4003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c4003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.584415 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.584420 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.584424 80          Options.compression: Snappy
2025/08/28-06:43:08.584428 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.584431 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.584433 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.584436 80             Options.num_levels: 7
2025/08/28-06:43:08.584439 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.584442 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.584446 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.584449 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.584456 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.584459 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.584462 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584465 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584468 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584472 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.584475 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584477 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584481 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.584483 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.584486 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.584489 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584492 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584495 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584497 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584500 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.584503 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584506 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.584509 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.584512 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.584515 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.584523 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.584527 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.584531 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.584536 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.584540 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.584543 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.584547 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.584549 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.584552 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.584555 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.584557 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.584560 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.584563 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.584566 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.584569 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.584571 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.584574 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.584585 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.584591 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.584595 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.584599 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.584601 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.584604 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.584608 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.584611 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.584614 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.584618 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.584621 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.584639 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.584643 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.584646 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.584651 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.584655 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.584658 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.584661 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.584664 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.584667 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.584670 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.584672 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.584675 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.584678 80                               Options.ttl: 2592000
2025/08/28-06:43:08.584681 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.584684 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.584686 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.584689 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.584692 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.584694 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.584697 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.584716 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.584721 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.584725 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.584729 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.584732 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.584735 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.599523 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.599529 80           Options.merge_operator: None
2025/08/28-06:43:08.599532 80        Options.compaction_filter: None
2025/08/28-06:43:08.599536 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.599539 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.599543 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.599547 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.599600 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c4005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c40061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.599606 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.599610 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.599613 80          Options.compression: Snappy
2025/08/28-06:43:08.599617 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.599620 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.599623 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.599626 80             Options.num_levels: 7
2025/08/28-06:43:08.599629 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.599633 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.599636 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.599639 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.599642 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.599645 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.599648 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599652 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599655 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599658 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.599661 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599668 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599672 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599675 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599678 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599681 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599684 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599687 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599690 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599694 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599697 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599712 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599717 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599720 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599723 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599726 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599729 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599734 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599739 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599744 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599747 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599750 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599752 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599756 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599758 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599761 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599764 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599767 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599770 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599773 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599776 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599779 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599782 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599785 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599789 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599792 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599795 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599798 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599801 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599804 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599807 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599810 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599813 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599821 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599825 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599828 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599832 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599836 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599839 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599842 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.599844 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599847 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599850 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599853 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599856 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599858 80                               Options.ttl: 2592000
2025/08/28-06:43:08.599861 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599864 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599867 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599870 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.599872 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.599875 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599878 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599881 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599885 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599889 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599893 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599896 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599899 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.615290 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.615299 80           Options.merge_operator: None
2025/08/28-06:43:08.615302 80        Options.compaction_filter: None
2025/08/28-06:43:08.615305 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.615307 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.615310 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.615313 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.615376 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c4008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c4008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.615382 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.615385 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.615388 80          Options.compression: Snappy
2025/08/28-06:43:08.615391 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.615394 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.615397 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.615399 80             Options.num_levels: 7
2025/08/28-06:43:08.615402 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.615405 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.615408 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.615412 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.615417 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.615420 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.615424 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615438 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615441 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615442 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.615444 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615445 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615446 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.615448 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.615449 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.615451 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615452 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615453 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615455 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615457 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.615458 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615460 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.615461 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.615462 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.615464 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.615465 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.615466 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.615468 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.615470 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.615472 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.615474 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.615476 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.615477 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.615478 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.615480 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.615481 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.615483 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.615484 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.615485 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.615486 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.615488 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.615490 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.615491 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.615493 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.615494 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.615496 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.615497 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.615499 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.615500 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.615501 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.615503 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.615504 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.615506 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.615510 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.615511 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.615512 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.615515 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.615516 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.615518 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.615519 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.615521 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.615522 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.615524 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.615525 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.615526 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.615527 80                               Options.ttl: 2592000
2025/08/28-06:43:08.615529 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.615530 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.615531 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.615533 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.615534 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.615535 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.615536 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.615538 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.615539 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.615541 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.615542 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.615544 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.615545 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.627876 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.627886 80           Options.merge_operator: None
2025/08/28-06:43:08.627889 80        Options.compaction_filter: None
2025/08/28-06:43:08.627891 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.627894 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.627897 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.627900 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.627948 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20c400ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20c400aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.627954 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.627957 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.627961 80          Options.compression: Snappy
2025/08/28-06:43:08.627964 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.627967 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.627970 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.627973 80             Options.num_levels: 7
2025/08/28-06:43:08.627976 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.627979 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.627982 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.627985 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.627989 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.627991 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.627994 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.627997 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.628000 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.628003 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.628006 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.628008 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.628011 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.628014 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.628017 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.628020 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.628022 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.628025 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.628028 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.628031 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.628034 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.628036 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.628039 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.628042 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.628045 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.628049 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.628051 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.628054 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.628059 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.628063 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.628066 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.628068 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.628071 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.628074 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.628076 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.628080 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.628082 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.628085 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.628088 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.628091 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.628094 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.628097 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.628099 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.628103 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.628107 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.628110 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.628112 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.628115 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.628118 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.628120 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.628123 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.628126 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.628129 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.628138 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.628141 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.628144 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.628147 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.628150 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.628153 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.628156 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.628159 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.628161 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.628164 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.628166 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.628169 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.628172 80                               Options.ttl: 2592000
2025/08/28-06:43:08.628175 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.628177 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.628180 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.628183 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.628186 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.628188 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.628191 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.628194 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.628198 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.628202 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.628205 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.628208 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.628211 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.650230 80 DB pointer 0x7f20c4014700
