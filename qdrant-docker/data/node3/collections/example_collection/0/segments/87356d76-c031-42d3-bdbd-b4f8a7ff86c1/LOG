2025/08/28-06:43:08.539601 74 RocksDB version: 8.1.1
2025/08/28-06:43:08.539629 74 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.539633 74 DB SUMMARY
2025/08/28-06:43:08.539636 74 DB Session ID:  24UZTVC9C92XPIYHBVR2
2025/08/28-06:43:08.540123 74 SST files in ./storage/collections/example_collection/0/segments/87356d76-c031-42d3-bdbd-b4f8a7ff86c1 dir, Total Num: 0, files: 
2025/08/28-06:43:08.540135 74 Write Ahead Log file in ./storage/collections/example_collection/0/segments/87356d76-c031-42d3-bdbd-b4f8a7ff86c1: 
2025/08/28-06:43:08.540140 74                         Options.error_if_exists: 0
2025/08/28-06:43:08.540144 74                       Options.create_if_missing: 1
2025/08/28-06:43:08.540147 74                         Options.paranoid_checks: 1
2025/08/28-06:43:08.540151 74             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.540154 74                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.540158 74        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.540161 74                                     Options.env: 0x565309db29c0
2025/08/28-06:43:08.540166 74                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.540169 74                                Options.info_log: 0x7f20dc011e00
2025/08/28-06:43:08.540173 74                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.540176 74                              Options.statistics: (nil)
2025/08/28-06:43:08.540179 74                               Options.use_fsync: 0
2025/08/28-06:43:08.540183 74                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.540186 74                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.540190 74                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.540193 74                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.540196 74                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.540199 74                         Options.allow_fallocate: 1
2025/08/28-06:43:08.540202 74                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.540205 74                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.540208 74                        Options.use_direct_reads: 0
2025/08/28-06:43:08.540211 74                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.540214 74          Options.create_missing_column_families: 1
2025/08/28-06:43:08.540217 74                              Options.db_log_dir: 
2025/08/28-06:43:08.540220 74                                 Options.wal_dir: 
2025/08/28-06:43:08.540223 74                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.540228 74                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.540231 74                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.540234 74                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.540237 74             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.540240 74                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.540243 74                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.540246 74                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.540249 74                    Options.write_buffer_manager: 0x7f20dc0113a0
2025/08/28-06:43:08.540253 74         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.540256 74           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.540259 74                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.540262 74                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.540266 74     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.540269 74                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.540272 74                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.540276 74                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.540278 74                  Options.unordered_write: 0
2025/08/28-06:43:08.540290 74         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.540293 74      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.540296 74             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.540303 74            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.540306 74                               Options.row_cache: None
2025/08/28-06:43:08.540309 74                              Options.wal_filter: None
2025/08/28-06:43:08.540312 74             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.540315 74             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.540318 74             Options.two_write_queues: 0
2025/08/28-06:43:08.540321 74             Options.manual_wal_flush: 0
2025/08/28-06:43:08.540324 74             Options.wal_compression: 0
2025/08/28-06:43:08.540327 74             Options.atomic_flush: 0
2025/08/28-06:43:08.540330 74             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.540333 74                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.540336 74                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.540339 74                 Options.log_readahead_size: 0
2025/08/28-06:43:08.540342 74                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.540345 74                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.540348 74                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.540351 74            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.540354 74             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.540358 74             Options.db_host_id: __hostname__
2025/08/28-06:43:08.540361 74             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.540364 74             Options.max_background_jobs: 2
2025/08/28-06:43:08.540367 74             Options.max_background_compactions: -1
2025/08/28-06:43:08.540403 74             Options.max_subcompactions: 1
2025/08/28-06:43:08.540413 74             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.540418 74           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.540422 74             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.540425 74             Options.max_total_wal_size: 0
2025/08/28-06:43:08.540428 74             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.540431 74                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.540434 74                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.540441 74                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.540445 74                          Options.max_open_files: 256
2025/08/28-06:43:08.540458 74                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.540463 74                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.540466 74                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.540469 74       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.540472 74                  Options.max_background_flushes: -1
2025/08/28-06:43:08.540476 74 Compression algorithms supported:
2025/08/28-06:43:08.540480 74 	kZSTD supported: 0
2025/08/28-06:43:08.540483 74 	kXpressCompression supported: 0
2025/08/28-06:43:08.540486 74 	kBZip2Compression supported: 0
2025/08/28-06:43:08.540489 74 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.540492 74 	kLZ4Compression supported: 0
2025/08/28-06:43:08.540495 74 	kZlibCompression supported: 0
2025/08/28-06:43:08.540498 74 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.540501 74 	kSnappyCompression supported: 1
2025/08/28-06:43:08.540506 74 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.540509 74 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.555038 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.555051 74           Options.merge_operator: None
2025/08/28-06:43:08.555056 74        Options.compaction_filter: None
2025/08/28-06:43:08.555067 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.555076 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.555080 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.555084 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.555179 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20dc00eac0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20dc00edf0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.555195 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.555227 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.555232 74          Options.compression: Snappy
2025/08/28-06:43:08.555242 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.555246 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.555250 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.555256 74             Options.num_levels: 7
2025/08/28-06:43:08.555261 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.555265 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.555277 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.555281 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.555286 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.555290 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.555294 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555298 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555301 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555306 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.555310 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555315 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555325 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.555333 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.555342 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.555346 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555356 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555360 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555373 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555378 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.555388 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555392 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.555395 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.555408 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.555412 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.555416 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.555419 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.555432 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.555439 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.555445 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.555448 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.555455 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.555458 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.555461 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.555465 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.555476 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.555479 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.555483 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.555910 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.555914 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.555918 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.555922 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.555925 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.555930 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.555934 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.555937 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.555941 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.555944 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.555947 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.555951 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.555954 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.555958 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.555961 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.555969 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.555972 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.555976 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.555991 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.555997 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.556000 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.556004 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.556007 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.556010 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.556013 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.556016 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.556051 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.556055 74                               Options.ttl: 2592000
2025/08/28-06:43:08.556058 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.556061 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.556065 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.556068 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.556071 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.556082 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.556086 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.556089 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.556118 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.556124 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.556128 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.556132 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.556135 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.590556 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.590567 74           Options.merge_operator: None
2025/08/28-06:43:08.590571 74        Options.compaction_filter: None
2025/08/28-06:43:08.590575 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.590578 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.590582 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.590586 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.590650 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20dc005150)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20dc005480
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.590658 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.590660 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.590663 74          Options.compression: Snappy
2025/08/28-06:43:08.590666 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.590668 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.590670 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.590672 74             Options.num_levels: 7
2025/08/28-06:43:08.590675 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.590678 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.590680 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.590683 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.590685 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.590688 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.590690 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590694 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590697 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590699 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.590886 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.590891 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590895 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.590911 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.590916 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.590919 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590923 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590927 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590930 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590934 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.590938 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.590941 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.590948 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.590951 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.590955 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.590959 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.590963 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.590966 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.590976 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.590981 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.590987 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.590992 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.591002 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.591006 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.591015 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.591019 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.591022 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.591028 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.591031 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.591036 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.591040 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.591044 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.591047 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.591052 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.591057 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.591061 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.591064 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.591071 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.591074 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.591078 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.591083 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.591087 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.591093 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.591105 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.591110 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.591113 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.591118 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.591123 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.591127 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.591132 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.591140 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.591144 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.591147 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.591154 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.591157 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.591161 74                               Options.ttl: 2592000
2025/08/28-06:43:08.591165 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.591168 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.591172 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.591176 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.591179 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.591183 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.591188 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.591191 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.591196 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.591201 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.591205 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.591209 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.591213 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601884 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601891 74           Options.merge_operator: None
2025/08/28-06:43:08.601896 74        Options.compaction_filter: None
2025/08/28-06:43:08.601899 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601901 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601904 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601907 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.601951 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20dc007760)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20dc007a90
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.601956 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.601959 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.601963 74          Options.compression: Snappy
2025/08/28-06:43:08.601966 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.601969 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.601971 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.601974 74             Options.num_levels: 7
2025/08/28-06:43:08.601977 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.601980 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.601982 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.601985 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.601988 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.601990 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.601993 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601996 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601998 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602001 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.602004 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602007 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602009 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.602012 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.602014 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.602016 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.602019 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.602021 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602024 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602027 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.602029 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602034 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.602037 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.602039 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.602042 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.602045 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.602047 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602076 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602099 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602108 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602112 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602115 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602118 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602121 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602124 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602127 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602131 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602134 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602138 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602141 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602144 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602148 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602152 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602156 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602160 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602164 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602167 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602170 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602173 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602177 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602181 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602184 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602187 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602197 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602201 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602204 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602209 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602213 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602216 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602220 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.602223 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602226 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602229 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602232 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602234 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602237 74                               Options.ttl: 2592000
2025/08/28-06:43:08.602240 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602244 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602247 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602250 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.602253 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.602256 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602260 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602263 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602267 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602271 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602274 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602277 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602281 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.619069 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.619080 74           Options.merge_operator: None
2025/08/28-06:43:08.619089 74        Options.compaction_filter: None
2025/08/28-06:43:08.619093 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.619096 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.619100 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.619103 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.619152 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20dc009dc0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20dc00a0f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.619158 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.619161 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.619165 74          Options.compression: Snappy
2025/08/28-06:43:08.619168 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.619171 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.619174 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.619177 74             Options.num_levels: 7
2025/08/28-06:43:08.619179 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.619182 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.619185 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.619188 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.619191 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.619194 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.619197 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619200 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619205 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619208 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.619210 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619213 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619216 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.619219 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.619223 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.619227 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619230 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619233 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619235 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619238 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.619241 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619244 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.619246 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.619249 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.619252 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.619256 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.619258 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.619261 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.619266 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.619270 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.619273 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.619276 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.619279 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.619281 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.619284 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.619287 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.619290 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.619293 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.619296 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.619298 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.619301 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.619304 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.619307 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.619310 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.619314 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.619316 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.619319 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.619322 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.619325 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.619328 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.619331 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.619333 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.619336 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.619347 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.619350 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.619353 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.619356 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.619360 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.619362 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.619365 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.619368 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.619371 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.619373 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.619376 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.619378 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.619381 74                               Options.ttl: 2592000
2025/08/28-06:43:08.619384 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.619386 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.619389 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.619392 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.619394 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.619397 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.619400 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.619403 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.619406 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.619410 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.619413 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.619416 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.619419 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637781 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637788 74           Options.merge_operator: None
2025/08/28-06:43:08.637790 74        Options.compaction_filter: None
2025/08/28-06:43:08.637793 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637795 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637798 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637801 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637844 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20dc00c440)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20dc00c770
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637849 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637851 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637855 74          Options.compression: Snappy
2025/08/28-06:43:08.637857 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637860 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637862 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637864 74             Options.num_levels: 7
2025/08/28-06:43:08.637867 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637870 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637872 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637874 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.637877 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.637879 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.637882 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637884 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637886 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637889 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.637891 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637893 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637896 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.637898 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.637900 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.637903 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637906 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637908 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637911 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637914 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.637916 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637919 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.637922 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.637924 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.637927 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.637929 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.637932 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.637934 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.637939 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.637942 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.637945 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.637948 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.637950 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.637952 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.637955 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.637958 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.637960 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.637963 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.637966 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.637968 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.637971 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.637974 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.637976 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.637979 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.637982 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.637985 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.637988 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.637990 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.637993 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.637995 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.637998 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638007 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638008 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638012 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638013 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638014 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638016 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638018 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638019 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638021 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.638022 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638023 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638025 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638026 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638027 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638029 74                               Options.ttl: 2592000
2025/08/28-06:43:08.638030 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638031 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638032 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638034 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.638035 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.638037 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638038 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638039 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638041 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638043 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638045 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638046 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638048 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.658901 74 DB pointer 0x7f20dc019140
