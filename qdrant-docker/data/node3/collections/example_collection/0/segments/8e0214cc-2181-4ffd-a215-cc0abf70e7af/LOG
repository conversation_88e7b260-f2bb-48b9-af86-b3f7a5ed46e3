2025/08/28-06:43:08.540839 79 RocksDB version: 8.1.1
2025/08/28-06:43:08.540875 79 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.540879 79 DB SUMMARY
2025/08/28-06:43:08.540882 79 DB Session ID:  24UZTVC9C92XPIYHBVQY
2025/08/28-06:43:08.541023 79 SST files in ./storage/collections/example_collection/0/segments/8e0214cc-2181-4ffd-a215-cc0abf70e7af dir, Total Num: 0, files: 
2025/08/28-06:43:08.541027 79 Write Ahead Log file in ./storage/collections/example_collection/0/segments/8e0214cc-2181-4ffd-a215-cc0abf70e7af: 
2025/08/28-06:43:08.541031 79                         Options.error_if_exists: 0
2025/08/28-06:43:08.541035 79                       Options.create_if_missing: 1
2025/08/28-06:43:08.541041 79                         Options.paranoid_checks: 1
2025/08/28-06:43:08.541044 79             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.541052 79                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.541057 79        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.541062 79                                     Options.env: 0x565309db29c0
2025/08/28-06:43:08.541065 79                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.541067 79                                Options.info_log: 0x7f20d0010540
2025/08/28-06:43:08.541070 79                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.541072 79                              Options.statistics: (nil)
2025/08/28-06:43:08.541075 79                               Options.use_fsync: 0
2025/08/28-06:43:08.541077 79                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.541079 79                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.541081 79                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.541084 79                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.541088 79                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.541090 79                         Options.allow_fallocate: 1
2025/08/28-06:43:08.541092 79                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.541094 79                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.541096 79                        Options.use_direct_reads: 0
2025/08/28-06:43:08.541098 79                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.541100 79          Options.create_missing_column_families: 1
2025/08/28-06:43:08.541104 79                              Options.db_log_dir: 
2025/08/28-06:43:08.541107 79                                 Options.wal_dir: 
2025/08/28-06:43:08.541110 79                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.541112 79                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.541116 79                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.541119 79                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.541123 79             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.541126 79                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.541129 79                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.541134 79                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.541138 79                    Options.write_buffer_manager: 0x7f20d000fae0
2025/08/28-06:43:08.541145 79         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.541149 79           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.541152 79                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.541156 79                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.541160 79     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.541164 79                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.541167 79                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.541171 79                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.541176 79                  Options.unordered_write: 0
2025/08/28-06:43:08.541185 79         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.541188 79      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.541192 79             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.541198 79            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.541201 79                               Options.row_cache: None
2025/08/28-06:43:08.541204 79                              Options.wal_filter: None
2025/08/28-06:43:08.541206 79             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.541210 79             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.541212 79             Options.two_write_queues: 0
2025/08/28-06:43:08.541214 79             Options.manual_wal_flush: 0
2025/08/28-06:43:08.541217 79             Options.wal_compression: 0
2025/08/28-06:43:08.541219 79             Options.atomic_flush: 0
2025/08/28-06:43:08.541220 79             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.541223 79                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.541225 79                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.541227 79                 Options.log_readahead_size: 0
2025/08/28-06:43:08.541230 79                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.541234 79                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.541237 79                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.541242 79            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.541246 79             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.541249 79             Options.db_host_id: __hostname__
2025/08/28-06:43:08.541256 79             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.541259 79             Options.max_background_jobs: 2
2025/08/28-06:43:08.541263 79             Options.max_background_compactions: -1
2025/08/28-06:43:08.541267 79             Options.max_subcompactions: 1
2025/08/28-06:43:08.541270 79             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.541273 79           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.541276 79             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.541280 79             Options.max_total_wal_size: 0
2025/08/28-06:43:08.541283 79             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.541286 79                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.541291 79                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.541295 79                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.541298 79                          Options.max_open_files: 256
2025/08/28-06:43:08.541303 79                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.541307 79                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.541312 79                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.541316 79       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.541319 79                  Options.max_background_flushes: -1
2025/08/28-06:43:08.541322 79 Compression algorithms supported:
2025/08/28-06:43:08.541325 79 	kZSTD supported: 0
2025/08/28-06:43:08.541328 79 	kXpressCompression supported: 0
2025/08/28-06:43:08.541331 79 	kBZip2Compression supported: 0
2025/08/28-06:43:08.541335 79 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.541339 79 	kLZ4Compression supported: 0
2025/08/28-06:43:08.541342 79 	kZlibCompression supported: 0
2025/08/28-06:43:08.541346 79 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.541350 79 	kSnappyCompression supported: 1
2025/08/28-06:43:08.541355 79 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.541361 79 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.551363 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.551374 79           Options.merge_operator: None
2025/08/28-06:43:08.551379 79        Options.compaction_filter: None
2025/08/28-06:43:08.551388 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.551393 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.551397 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.551400 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.551996 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d000d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d000d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.552008 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.552014 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.552020 79          Options.compression: Snappy
2025/08/28-06:43:08.552025 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.552030 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.552034 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.552037 79             Options.num_levels: 7
2025/08/28-06:43:08.552041 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.552044 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.552048 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.552052 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.552058 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.552062 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.552066 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.552071 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.552075 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.552079 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.552084 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.552088 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.552093 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.552102 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.552106 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.552110 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.552113 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.552118 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.552122 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.552125 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.552128 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.552131 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.552134 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.552144 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.552148 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.552179 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.552184 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.552188 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.552195 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.552201 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.552206 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.552210 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.552213 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.552217 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.552220 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.552222 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.552225 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.552227 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.552230 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.552233 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.552235 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.552238 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.552240 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.552245 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.552251 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.552254 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.552257 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.552259 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.552262 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.552265 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.552268 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.552270 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.552273 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.552284 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.552287 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.552289 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.552293 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.552296 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.552299 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.552302 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.552304 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.552307 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.552310 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.552312 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.552314 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.552317 79                               Options.ttl: 2592000
2025/08/28-06:43:08.552319 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.552322 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.552324 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.552327 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.552329 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.552335 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.552338 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.552344 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.552348 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.552351 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.552354 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.552356 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.552359 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.565617 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.565630 79           Options.merge_operator: None
2025/08/28-06:43:08.565635 79        Options.compaction_filter: None
2025/08/28-06:43:08.565638 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.565641 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.565645 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.565648 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.566186 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d0003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d0003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.566196 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.566200 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.566206 79          Options.compression: Snappy
2025/08/28-06:43:08.566211 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.566214 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.566218 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.566222 79             Options.num_levels: 7
2025/08/28-06:43:08.566226 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.566230 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.566233 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.566237 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.566241 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.566245 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.566249 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.566253 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.566256 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.566335 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.566341 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.566345 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.566354 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.566360 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.566378 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.566384 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.566392 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.566452 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.566457 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.566461 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.566464 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.566473 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.566478 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.566482 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.566485 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.566489 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.566498 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.566503 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.566511 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.566516 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.566520 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.566527 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.566533 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.566536 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.566539 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.566542 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.566552 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.566556 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.566559 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.566563 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.566572 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.566577 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.566580 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.566585 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.566590 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.566601 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.566605 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.566609 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.566612 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.566616 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.566620 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.566624 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.566627 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.566644 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.566648 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.566652 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.566657 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.566662 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.566666 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.566669 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.566672 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.566676 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.566679 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.566683 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.566686 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.566689 79                               Options.ttl: 2592000
2025/08/28-06:43:08.566692 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.566696 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.566699 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.566718 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.566722 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.566725 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.566729 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.566740 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.566746 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.566751 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.566755 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.566759 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.566763 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.590544 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.590554 79           Options.merge_operator: None
2025/08/28-06:43:08.590558 79        Options.compaction_filter: None
2025/08/28-06:43:08.590562 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.590565 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.590569 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.590572 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.590647 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d0005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d00061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.590653 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.590656 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.590659 79          Options.compression: Snappy
2025/08/28-06:43:08.590661 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.590663 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.590666 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.590669 79             Options.num_levels: 7
2025/08/28-06:43:08.590671 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.590674 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.590676 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.590684 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.590687 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.590690 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.590694 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590696 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590698 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590920 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.590924 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.590931 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590935 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.590939 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.590943 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.590947 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590951 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590954 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590959 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590963 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.590966 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.590971 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.590975 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.590979 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.590983 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.590987 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.590991 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.590995 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.591000 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.591005 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.591027 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.591031 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.591035 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.591039 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.591043 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.591047 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.591050 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.591054 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.591058 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.591062 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.591065 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.591069 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.591074 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.591078 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.591082 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.591086 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.591089 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.591093 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.591098 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.591101 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.591105 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.591110 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.591115 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.591121 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.591125 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.591129 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.591133 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.591138 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.591143 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.591147 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.591170 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.591174 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.591178 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.591183 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.591222 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.591227 79                               Options.ttl: 2592000
2025/08/28-06:43:08.591232 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.591239 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.591245 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.591248 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.591252 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.591255 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.591258 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.591261 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.591266 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.591271 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.591274 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.591277 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.591281 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601684 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601695 79           Options.merge_operator: None
2025/08/28-06:43:08.601698 79        Options.compaction_filter: None
2025/08/28-06:43:08.601714 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601717 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601721 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601725 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.601789 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d0008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d0008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.601794 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.601797 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.601801 79          Options.compression: Snappy
2025/08/28-06:43:08.601804 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.601807 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.601810 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.601813 79             Options.num_levels: 7
2025/08/28-06:43:08.601817 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.601820 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.601824 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.601827 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.601831 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.601834 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.601838 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601841 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601843 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601847 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.601850 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601853 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601856 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.601860 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.601863 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.601866 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601869 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601872 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601875 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601878 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.601882 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601885 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.601888 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.601892 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.601899 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.601903 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.601906 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.601909 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.601914 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.601918 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.601922 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.601925 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.601928 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.601931 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.601934 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.601937 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.601941 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.601944 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.601947 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.601950 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.601953 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.601957 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.601959 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.601963 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.601966 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.601970 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.601973 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.601984 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.601988 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.601991 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.601995 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.601998 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602001 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602012 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602015 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602018 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602022 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602026 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602029 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602033 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.602037 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602040 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602043 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602046 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602049 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602052 79                               Options.ttl: 2592000
2025/08/28-06:43:08.602055 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602058 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602061 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602065 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.602068 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.602070 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602074 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602077 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602082 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602086 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602090 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602093 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602097 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.617522 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.617530 79           Options.merge_operator: None
2025/08/28-06:43:08.617534 79        Options.compaction_filter: None
2025/08/28-06:43:08.617537 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.617540 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.617543 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.617545 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.617602 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f20d000ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f20d000aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.617611 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.617615 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.617618 79          Options.compression: Snappy
2025/08/28-06:43:08.617621 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.617624 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.617627 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.617630 79             Options.num_levels: 7
2025/08/28-06:43:08.617633 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.617636 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.617638 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.617641 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.617644 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.617647 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.617650 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617653 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617656 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617658 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.617661 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617664 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617667 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.617670 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.617672 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.617675 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617677 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617680 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617682 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617683 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.617684 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617686 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.617687 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.617688 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.617690 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.617691 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.617692 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.617694 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.617696 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.617698 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.617707 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.617709 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.617710 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.617712 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.617713 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.617714 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.617716 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.617717 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.617719 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.617720 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.617721 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.617723 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.617724 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.617726 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.617727 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.617729 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.617730 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.617731 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.617733 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.617734 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.617736 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.617737 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.617738 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.617743 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.617744 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.617746 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.617747 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.617749 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.617750 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.617752 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.617753 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.617754 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.617755 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.617757 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.617758 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.617759 79                               Options.ttl: 2592000
2025/08/28-06:43:08.617760 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.617762 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.617763 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.617764 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.617766 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.617767 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.617768 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.617770 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.617771 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.617773 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.617775 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.617776 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.617777 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.645899 79 DB pointer 0x7f20d0014700
