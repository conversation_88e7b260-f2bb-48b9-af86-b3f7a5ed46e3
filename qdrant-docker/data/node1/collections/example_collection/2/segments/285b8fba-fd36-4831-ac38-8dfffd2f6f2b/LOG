2025/08/28-06:43:08.536812 80 RocksDB version: 8.1.1
2025/08/28-06:43:08.537057 80 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.537062 80 DB SUMMARY
2025/08/28-06:43:08.537067 80 DB Session ID:  T43XGGKDKN4XM6MRSEDS
2025/08/28-06:43:08.537116 80 SST files in ./storage/collections/example_collection/2/segments/285b8fba-fd36-4831-ac38-8dfffd2f6f2b dir, Total Num: 0, files: 
2025/08/28-06:43:08.537245 80 Write Ahead Log file in ./storage/collections/example_collection/2/segments/285b8fba-fd36-4831-ac38-8dfffd2f6f2b: 
2025/08/28-06:43:08.537249 80                         Options.error_if_exists: 0
2025/08/28-06:43:08.537255 80                       Options.create_if_missing: 1
2025/08/28-06:43:08.537259 80                         Options.paranoid_checks: 1
2025/08/28-06:43:08.537263 80             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.537270 80                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.537275 80        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.537279 80                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.537291 80                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.537295 80                                Options.info_log: 0x7f64f8010540
2025/08/28-06:43:08.537299 80                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.537304 80                              Options.statistics: (nil)
2025/08/28-06:43:08.537312 80                               Options.use_fsync: 0
2025/08/28-06:43:08.537316 80                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.537320 80                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.537324 80                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.537329 80                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.537333 80                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.537412 80                         Options.allow_fallocate: 1
2025/08/28-06:43:08.537637 80                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.537639 80                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.537643 80                        Options.use_direct_reads: 0
2025/08/28-06:43:08.537646 80                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.537651 80          Options.create_missing_column_families: 1
2025/08/28-06:43:08.537655 80                              Options.db_log_dir: 
2025/08/28-06:43:08.537661 80                                 Options.wal_dir: 
2025/08/28-06:43:08.537665 80                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.537668 80                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.537672 80                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.537675 80                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.537680 80             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.537687 80                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.537692 80                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.537695 80                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.537699 80                    Options.write_buffer_manager: 0x7f64f800fae0
2025/08/28-06:43:08.537718 80         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.537722 80           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.537726 80                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.537730 80                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.537733 80     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.537737 80                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.537740 80                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.537744 80                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.537747 80                  Options.unordered_write: 0
2025/08/28-06:43:08.537756 80         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.537761 80      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.537765 80             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.537769 80            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.537772 80                               Options.row_cache: None
2025/08/28-06:43:08.537795 80                              Options.wal_filter: None
2025/08/28-06:43:08.537800 80             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.537804 80             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.537807 80             Options.two_write_queues: 0
2025/08/28-06:43:08.537811 80             Options.manual_wal_flush: 0
2025/08/28-06:43:08.537815 80             Options.wal_compression: 0
2025/08/28-06:43:08.537820 80             Options.atomic_flush: 0
2025/08/28-06:43:08.537825 80             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.537828 80                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.537833 80                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.537838 80                 Options.log_readahead_size: 0
2025/08/28-06:43:08.537843 80                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.537848 80                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.537851 80                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.537856 80            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.537859 80             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.537862 80             Options.db_host_id: __hostname__
2025/08/28-06:43:08.537870 80             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.537874 80             Options.max_background_jobs: 2
2025/08/28-06:43:08.537878 80             Options.max_background_compactions: -1
2025/08/28-06:43:08.537903 80             Options.max_subcompactions: 1
2025/08/28-06:43:08.537907 80             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.537910 80           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.537913 80             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.537916 80             Options.max_total_wal_size: 0
2025/08/28-06:43:08.537920 80             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.537923 80                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.537926 80                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.537929 80                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.537933 80                          Options.max_open_files: 256
2025/08/28-06:43:08.537937 80                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.537939 80                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.537942 80                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.537945 80       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.537949 80                  Options.max_background_flushes: -1
2025/08/28-06:43:08.537952 80 Compression algorithms supported:
2025/08/28-06:43:08.537956 80 	kZSTD supported: 0
2025/08/28-06:43:08.537960 80 	kXpressCompression supported: 0
2025/08/28-06:43:08.537963 80 	kBZip2Compression supported: 0
2025/08/28-06:43:08.537966 80 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.537970 80 	kLZ4Compression supported: 0
2025/08/28-06:43:08.537973 80 	kZlibCompression supported: 0
2025/08/28-06:43:08.537976 80 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.537978 80 	kSnappyCompression supported: 1
2025/08/28-06:43:08.537982 80 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.537986 80 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.556128 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.556143 80           Options.merge_operator: None
2025/08/28-06:43:08.556148 80        Options.compaction_filter: None
2025/08/28-06:43:08.556174 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.556178 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.556183 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.556187 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.556296 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f800d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f800d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.556303 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.556308 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.556313 80          Options.compression: Snappy
2025/08/28-06:43:08.556318 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.556322 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.556325 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.556330 80             Options.num_levels: 7
2025/08/28-06:43:08.556333 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.556337 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.556345 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.556349 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.556353 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.556357 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.556361 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556365 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556368 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556373 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.556377 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556381 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556385 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.556389 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.556398 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.556404 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556408 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556412 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556416 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556422 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.556426 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556432 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.556438 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.556446 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.556453 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.556457 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.556461 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.556465 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.556472 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.556478 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.556482 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.556487 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.556490 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.556495 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.556498 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.556502 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.556506 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.556510 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.556516 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.556524 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.556528 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.556536 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.556541 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.556546 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.556555 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.556559 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.556563 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.556567 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.556571 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.556575 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.556579 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.556584 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.556590 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.556605 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.556609 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.556613 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.556618 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.556627 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.556631 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.556636 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.556639 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.556644 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.556648 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.556651 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.556659 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.556663 80                               Options.ttl: 2592000
2025/08/28-06:43:08.556681 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.556689 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.556693 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.556697 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.556782 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.556794 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.556799 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.556808 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.556814 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.556819 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.556825 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.556829 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.556833 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.584222 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.584233 80           Options.merge_operator: None
2025/08/28-06:43:08.584239 80        Options.compaction_filter: None
2025/08/28-06:43:08.584243 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.584246 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.584249 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.584253 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.584319 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f8003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f8003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.584325 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.584329 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.584333 80          Options.compression: Snappy
2025/08/28-06:43:08.584338 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.584341 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.584345 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.584348 80             Options.num_levels: 7
2025/08/28-06:43:08.584352 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.584356 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.584362 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.584365 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.584369 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.584373 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.584378 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584381 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584386 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584390 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.584394 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584397 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584401 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.584405 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.584408 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.584413 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584416 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584420 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584423 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584427 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.584431 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584434 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.584438 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.584441 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.584445 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.584449 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.584452 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.584456 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.584462 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.584467 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.584474 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.584478 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.584482 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.584486 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.584489 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.584493 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.584496 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.584499 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.584503 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.584507 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.584510 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.584514 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.584517 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.584521 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.584526 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.584529 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.584536 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.584539 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.584543 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.584547 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.584551 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.584555 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.584560 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.584572 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.584578 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.584583 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.584587 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.584592 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.584596 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.584604 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.584607 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.584613 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.584616 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.584621 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.584627 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.584632 80                               Options.ttl: 2592000
2025/08/28-06:43:08.584636 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.584640 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.584643 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.584647 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.584651 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.584655 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.584658 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.584662 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.584668 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.584673 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.584677 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.584681 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.584686 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601853 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601863 80           Options.merge_operator: None
2025/08/28-06:43:08.601867 80        Options.compaction_filter: None
2025/08/28-06:43:08.601871 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601874 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601885 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601889 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.601952 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f8005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f80061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.601992 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.601996 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.602005 80          Options.compression: Snappy
2025/08/28-06:43:08.602009 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.602013 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.602016 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.602019 80             Options.num_levels: 7
2025/08/28-06:43:08.602026 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.602030 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.602037 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.602040 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.602044 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.602048 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.602054 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.602058 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.602061 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602065 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.602068 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602072 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602075 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.602079 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.602082 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.602087 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.602091 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.602095 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602098 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602102 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.602105 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602110 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.602114 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.602118 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.602121 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.602124 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.602131 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602134 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602140 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602145 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602156 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602160 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602163 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602169 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602172 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602175 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602179 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602182 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602185 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602188 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602192 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602195 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602199 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602203 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602207 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602210 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602213 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602216 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602220 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602223 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602227 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602231 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602234 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602242 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602246 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602249 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602253 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602257 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602260 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602264 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.602267 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602270 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602272 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602275 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602278 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602281 80                               Options.ttl: 2592000
2025/08/28-06:43:08.602284 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602287 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602290 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602293 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.602296 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.602299 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602303 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602306 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602310 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602314 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602318 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602321 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602325 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.617466 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.617477 80           Options.merge_operator: None
2025/08/28-06:43:08.617486 80        Options.compaction_filter: None
2025/08/28-06:43:08.617490 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.617493 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.617499 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.617502 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.617551 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f8008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f8008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.617677 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.617682 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.617684 80          Options.compression: Snappy
2025/08/28-06:43:08.617685 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.617687 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.617688 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.617690 80             Options.num_levels: 7
2025/08/28-06:43:08.617691 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.617692 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.617694 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.617695 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.617697 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.617698 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.617699 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617714 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617716 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617717 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.617718 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617720 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617721 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.617723 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.617724 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.617726 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617727 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617728 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617730 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617731 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.617732 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617734 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.617735 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.617736 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.617738 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.617739 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.617741 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.617742 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.617744 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.617746 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.617747 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.617748 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.617749 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.617751 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.617752 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.617753 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.617755 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.617756 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.617757 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.617759 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.617760 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.617762 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.617763 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.617765 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.617766 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.617768 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.617769 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.617770 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.617772 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.617773 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.617775 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.617776 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.617777 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.617781 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.617782 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.617784 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.617785 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.617787 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.617788 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.617790 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.617791 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.617793 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.617794 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.617795 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.617797 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.617798 80                               Options.ttl: 2592000
2025/08/28-06:43:08.617800 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.617801 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.617802 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.617804 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.617805 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.617806 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.617808 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.617809 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.617811 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.617813 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.617814 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.617816 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.617817 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637795 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637805 80           Options.merge_operator: None
2025/08/28-06:43:08.637808 80        Options.compaction_filter: None
2025/08/28-06:43:08.637811 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637814 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637817 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637820 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637871 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f800ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f800aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637876 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637879 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637883 80          Options.compression: Snappy
2025/08/28-06:43:08.637885 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637888 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637890 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637893 80             Options.num_levels: 7
2025/08/28-06:43:08.637898 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637902 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637904 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637907 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.637913 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.637917 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.637921 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637924 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637927 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637930 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.637955 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637958 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637961 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.637963 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.637968 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.637972 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637975 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637978 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637993 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637995 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.637998 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638001 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.638007 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.638009 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.638010 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.638012 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.638013 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.638015 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.638018 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.638021 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.638023 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.638025 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.638027 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.638029 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.638032 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.638034 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.638036 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.638038 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.638040 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.638042 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.638044 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.638046 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.638048 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.638050 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.638053 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.638055 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.638057 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.638059 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.638061 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.638063 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.638065 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638067 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638069 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638075 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638077 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638079 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638082 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638084 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638086 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638089 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.638091 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638093 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638095 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638097 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638099 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638102 80                               Options.ttl: 2592000
2025/08/28-06:43:08.638104 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638106 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638108 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638109 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.638111 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.638113 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638115 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638117 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638119 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638121 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638123 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638124 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638126 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.658896 80 DB pointer 0x7f64f8014700
