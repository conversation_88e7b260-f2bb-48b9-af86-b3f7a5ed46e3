2025/08/28-06:43:08.659098 80 RocksDB version: 8.1.1
2025/08/28-06:43:08.659107 80 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.659108 80 DB SUMMARY
2025/08/28-06:43:08.659109 80 DB Session ID:  T43XGGKDKN4XM6MRSEE3
2025/08/28-06:43:08.659118 80 SST files in ./storage/collections/example_collection/2/segments/285b8fba-fd36-4831-ac38-8dfffd2f6f2b/payload_index dir, Total Num: 0, files: 
2025/08/28-06:43:08.659120 80 Write Ahead Log file in ./storage/collections/example_collection/2/segments/285b8fba-fd36-4831-ac38-8dfffd2f6f2b/payload_index: 
2025/08/28-06:43:08.659121 80                         Options.error_if_exists: 0
2025/08/28-06:43:08.659122 80                       Options.create_if_missing: 1
2025/08/28-06:43:08.659123 80                         Options.paranoid_checks: 1
2025/08/28-06:43:08.659124 80             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.659125 80                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.659126 80        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.659127 80                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.659128 80                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.659129 80                                Options.info_log: 0x7f64f8084370
2025/08/28-06:43:08.659130 80                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.659131 80                              Options.statistics: (nil)
2025/08/28-06:43:08.659132 80                               Options.use_fsync: 0
2025/08/28-06:43:08.659133 80                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.659134 80                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.659135 80                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.659136 80                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.659137 80                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.659138 80                         Options.allow_fallocate: 1
2025/08/28-06:43:08.659139 80                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.659139 80                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.659140 80                        Options.use_direct_reads: 0
2025/08/28-06:43:08.659141 80                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.659142 80          Options.create_missing_column_families: 1
2025/08/28-06:43:08.659143 80                              Options.db_log_dir: 
2025/08/28-06:43:08.659144 80                                 Options.wal_dir: 
2025/08/28-06:43:08.659144 80                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.659145 80                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.659146 80                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.659147 80                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.659148 80             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.659149 80                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.659150 80                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.659150 80                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.659151 80                    Options.write_buffer_manager: 0x7f64f80c45f0
2025/08/28-06:43:08.659152 80         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.659153 80           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.659154 80                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.659155 80                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.659156 80     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.659157 80                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.659158 80                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.659159 80                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.659160 80                  Options.unordered_write: 0
2025/08/28-06:43:08.659161 80         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.659161 80      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.659162 80             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.659163 80            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.659164 80                               Options.row_cache: None
2025/08/28-06:43:08.659165 80                              Options.wal_filter: None
2025/08/28-06:43:08.659166 80             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.659167 80             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.659167 80             Options.two_write_queues: 0
2025/08/28-06:43:08.659168 80             Options.manual_wal_flush: 0
2025/08/28-06:43:08.659169 80             Options.wal_compression: 0
2025/08/28-06:43:08.659170 80             Options.atomic_flush: 0
2025/08/28-06:43:08.659171 80             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.659172 80                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.659173 80                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.659174 80                 Options.log_readahead_size: 0
2025/08/28-06:43:08.659174 80                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.659175 80                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.659176 80                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.659177 80            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.659178 80             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.659179 80             Options.db_host_id: __hostname__
2025/08/28-06:43:08.659180 80             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.659181 80             Options.max_background_jobs: 2
2025/08/28-06:43:08.659182 80             Options.max_background_compactions: -1
2025/08/28-06:43:08.659183 80             Options.max_subcompactions: 1
2025/08/28-06:43:08.659183 80             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.659184 80           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.659185 80             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.659186 80             Options.max_total_wal_size: 0
2025/08/28-06:43:08.659187 80             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.659188 80                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.659189 80                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.659190 80                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.659191 80                          Options.max_open_files: 256
2025/08/28-06:43:08.659191 80                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.659192 80                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.659193 80                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.659194 80       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.659195 80                  Options.max_background_flushes: -1
2025/08/28-06:43:08.659196 80 Compression algorithms supported:
2025/08/28-06:43:08.659197 80 	kZSTD supported: 0
2025/08/28-06:43:08.659198 80 	kXpressCompression supported: 0
2025/08/28-06:43:08.659198 80 	kBZip2Compression supported: 0
2025/08/28-06:43:08.659200 80 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.659200 80 	kLZ4Compression supported: 0
2025/08/28-06:43:08.659201 80 	kZlibCompression supported: 0
2025/08/28-06:43:08.659202 80 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.659203 80 	kSnappyCompression supported: 1
2025/08/28-06:43:08.659205 80 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.659206 80 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.666586 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.666588 80           Options.merge_operator: None
2025/08/28-06:43:08.666589 80        Options.compaction_filter: None
2025/08/28-06:43:08.666605 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.666606 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.666608 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.666609 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.666625 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f807ea70)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f80c6a30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.666627 80        Options.write_buffer_size: 10485760
2025/08/28-06:43:08.666629 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.666631 80          Options.compression: Snappy
2025/08/28-06:43:08.666714 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.666715 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.666717 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.666718 80             Options.num_levels: 7
2025/08/28-06:43:08.666719 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.666721 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.666722 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.666723 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.666724 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.666726 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.666727 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.666729 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.666730 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.666732 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.666733 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.666734 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.666735 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.666736 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.666737 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.666739 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.666740 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.666741 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.666743 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.666744 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.666746 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.666747 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.666749 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.666750 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.666752 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.666753 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.666755 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.666756 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.666759 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.666761 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.666763 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.666765 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.666766 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.666768 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.666769 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.666770 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.666772 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.666773 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.666774 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.666776 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.666778 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.666779 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.666781 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.666783 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.666784 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.666786 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.666787 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.666789 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.666791 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.666793 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.666794 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.666796 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.666797 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.666802 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.666804 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.666805 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.666807 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.666809 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.666810 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.666811 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.666813 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.666814 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.666815 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.666816 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.666817 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.666818 80                               Options.ttl: 2592000
2025/08/28-06:43:08.666819 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.666820 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.666822 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.666823 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.666825 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.666826 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.666828 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.666829 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.666831 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.666832 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.666834 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.666835 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.666836 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.682019 80 DB pointer 0x7f64f8082780
