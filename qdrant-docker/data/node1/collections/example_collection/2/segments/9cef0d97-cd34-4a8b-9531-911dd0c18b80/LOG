2025/08/28-06:43:08.536776 79 RocksDB version: 8.1.1
2025/08/28-06:43:08.537463 79 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.537466 79 DB SUMMARY
2025/08/28-06:43:08.537470 79 DB Session ID:  T43XGGKDKN4XM6MRSEDT
2025/08/28-06:43:08.537515 79 SST files in ./storage/collections/example_collection/2/segments/9cef0d97-cd34-4a8b-9531-911dd0c18b80 dir, Total Num: 0, files: 
2025/08/28-06:43:08.537518 79 Write Ahead Log file in ./storage/collections/example_collection/2/segments/9cef0d97-cd34-4a8b-9531-911dd0c18b80: 
2025/08/28-06:43:08.537526 79                         Options.error_if_exists: 0
2025/08/28-06:43:08.537529 79                       Options.create_if_missing: 1
2025/08/28-06:43:08.537532 79                         Options.paranoid_checks: 1
2025/08/28-06:43:08.537535 79             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.537541 79                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.537544 79        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.537549 79                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.537558 79                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.537561 79                                Options.info_log: 0x7f64f4010540
2025/08/28-06:43:08.537566 79                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.537570 79                              Options.statistics: (nil)
2025/08/28-06:43:08.537573 79                               Options.use_fsync: 0
2025/08/28-06:43:08.537576 79                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.537579 79                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.537583 79                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.537586 79                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.537589 79                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.537595 79                         Options.allow_fallocate: 1
2025/08/28-06:43:08.537598 79                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.537601 79                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.537604 79                        Options.use_direct_reads: 0
2025/08/28-06:43:08.537609 79                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.537612 79          Options.create_missing_column_families: 1
2025/08/28-06:43:08.537617 79                              Options.db_log_dir: 
2025/08/28-06:43:08.537620 79                                 Options.wal_dir: 
2025/08/28-06:43:08.537623 79                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.537628 79                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.537631 79                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.537663 79                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.537668 79             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.537675 79                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.537679 79                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.537682 79                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.537687 79                    Options.write_buffer_manager: 0x7f64f400fae0
2025/08/28-06:43:08.537692 79         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.537696 79           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.537699 79                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.537718 79                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.537721 79     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.537725 79                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.537727 79                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.537730 79                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.537735 79                  Options.unordered_write: 0
2025/08/28-06:43:08.537742 79         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.537747 79      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.537755 79             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.537758 79            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.537762 79                               Options.row_cache: None
2025/08/28-06:43:08.537766 79                              Options.wal_filter: None
2025/08/28-06:43:08.537772 79             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.537787 79             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.537790 79             Options.two_write_queues: 0
2025/08/28-06:43:08.537795 79             Options.manual_wal_flush: 0
2025/08/28-06:43:08.537802 79             Options.wal_compression: 0
2025/08/28-06:43:08.537805 79             Options.atomic_flush: 0
2025/08/28-06:43:08.537808 79             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.537812 79                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.537815 79                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.537818 79                 Options.log_readahead_size: 0
2025/08/28-06:43:08.537821 79                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.537825 79                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.537828 79                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.537837 79            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.537841 79             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.537844 79             Options.db_host_id: __hostname__
2025/08/28-06:43:08.537847 79             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.537850 79             Options.max_background_jobs: 2
2025/08/28-06:43:08.537855 79             Options.max_background_compactions: -1
2025/08/28-06:43:08.537859 79             Options.max_subcompactions: 1
2025/08/28-06:43:08.537862 79             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.537865 79           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.537869 79             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.537872 79             Options.max_total_wal_size: 0
2025/08/28-06:43:08.537875 79             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.537879 79                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.537882 79                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.537885 79                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.537888 79                          Options.max_open_files: 256
2025/08/28-06:43:08.537891 79                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.537894 79                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.537897 79                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.537900 79       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.537903 79                  Options.max_background_flushes: -1
2025/08/28-06:43:08.537906 79 Compression algorithms supported:
2025/08/28-06:43:08.537910 79 	kZSTD supported: 0
2025/08/28-06:43:08.537914 79 	kXpressCompression supported: 0
2025/08/28-06:43:08.537917 79 	kBZip2Compression supported: 0
2025/08/28-06:43:08.537921 79 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.537924 79 	kLZ4Compression supported: 0
2025/08/28-06:43:08.537927 79 	kZlibCompression supported: 0
2025/08/28-06:43:08.537930 79 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.537934 79 	kSnappyCompression supported: 1
2025/08/28-06:43:08.537940 79 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.537943 79 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.556404 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.556418 79           Options.merge_operator: None
2025/08/28-06:43:08.556423 79        Options.compaction_filter: None
2025/08/28-06:43:08.556432 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.556439 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.556444 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.556449 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.556526 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f400d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f400d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.556533 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.556538 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.556544 79          Options.compression: Snappy
2025/08/28-06:43:08.556548 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.556552 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.556560 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.556564 79             Options.num_levels: 7
2025/08/28-06:43:08.556569 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.556573 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.556578 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.556582 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.556588 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.556593 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.556597 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556604 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556613 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556617 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.557088 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.557092 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.557096 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.557099 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.557103 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.557107 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.557117 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.557120 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.557124 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.557128 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.557132 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.557136 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.557143 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.557152 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.557156 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.557160 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.557164 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.557168 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.557175 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.557181 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.557185 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.557188 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.557191 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.557195 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.557198 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.557202 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.557206 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.557210 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.557214 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.557217 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.557221 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.557225 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.557229 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.557233 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.557237 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.557242 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.557249 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.557254 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.557258 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.557262 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.557266 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.557269 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.557272 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.557279 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.557282 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.557286 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.557291 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.557296 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.557299 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.557303 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.557306 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.557310 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.557314 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.557317 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.557321 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.557325 79                               Options.ttl: 2592000
2025/08/28-06:43:08.557329 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.557333 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.557336 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.557341 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.557345 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.557366 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.557371 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.557374 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.557380 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.557385 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.557395 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.557400 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.557404 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.578472 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.578488 79           Options.merge_operator: None
2025/08/28-06:43:08.578492 79        Options.compaction_filter: None
2025/08/28-06:43:08.578496 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.578501 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.578505 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.578509 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.578592 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f4003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f4003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.578601 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.578606 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.578611 79          Options.compression: Snappy
2025/08/28-06:43:08.578615 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.578618 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.578622 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.578625 79             Options.num_levels: 7
2025/08/28-06:43:08.578629 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.578632 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.578636 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.578640 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.578645 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.578649 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.578657 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.578661 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.578664 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.578669 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.578672 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.578676 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.578680 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.578684 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.578689 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.578693 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.578697 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.578721 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.578725 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.578729 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.578733 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.578737 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.578741 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.578745 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.578749 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.578753 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.578756 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.578761 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.578768 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.578774 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.578778 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.578782 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.578785 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.578789 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.578793 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.578798 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.578802 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.578805 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.578810 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.578819 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.578823 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.578828 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.578832 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.578838 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.578843 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.578847 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.578851 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.578856 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.578860 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.578865 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.578869 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.578873 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.578877 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.578895 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.578899 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.578903 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.578908 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.578913 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.578917 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.578922 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.578925 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.578927 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.578930 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.578935 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.578937 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.578943 79                               Options.ttl: 2592000
2025/08/28-06:43:08.578946 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.578948 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.578955 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.578958 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.578961 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.578964 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.578967 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.578972 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.578977 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.578982 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.578986 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.578990 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.578994 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.598857 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.598868 79           Options.merge_operator: None
2025/08/28-06:43:08.598870 79        Options.compaction_filter: None
2025/08/28-06:43:08.598873 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.598875 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.598877 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.598880 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.598947 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f4005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f40061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.598953 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.598956 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.598959 79          Options.compression: Snappy
2025/08/28-06:43:08.598962 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.598964 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.598966 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.598968 79             Options.num_levels: 7
2025/08/28-06:43:08.598971 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.598974 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.598976 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.598978 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.598983 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.598985 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.598987 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.598990 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.598992 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.598994 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.598998 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599000 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599003 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599005 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599007 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599010 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599015 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599017 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599019 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599021 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599024 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599026 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599029 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599032 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599034 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599036 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599039 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599041 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599045 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599048 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599051 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599054 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599056 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599057 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599059 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599062 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599064 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599066 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599068 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599071 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599073 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599075 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599078 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599085 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599088 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599090 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599092 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599094 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599097 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599101 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599104 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599110 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599113 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599125 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599129 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599131 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599137 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599141 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599144 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599148 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.599151 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599154 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599157 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599162 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599165 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599169 79                               Options.ttl: 2592000
2025/08/28-06:43:08.599173 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599176 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599178 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599180 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.599184 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.599186 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599190 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599194 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599199 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599203 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599206 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599210 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599214 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.615446 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.615451 79           Options.merge_operator: None
2025/08/28-06:43:08.615454 79        Options.compaction_filter: None
2025/08/28-06:43:08.615456 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.615458 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.615461 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.615463 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.615492 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f4008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f4008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.615495 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.615497 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.615498 79          Options.compression: Snappy
2025/08/28-06:43:08.615500 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.615501 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.615503 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.615504 79             Options.num_levels: 7
2025/08/28-06:43:08.615506 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.615507 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.615508 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.615510 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.615512 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.615513 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.615515 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615516 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615518 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615519 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.615521 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615522 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615523 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.615525 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.615526 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.615528 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615529 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615530 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615532 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615533 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.615534 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615536 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.615538 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.615540 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.615541 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.615543 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.615544 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.615545 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.615548 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.615550 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.615551 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.615553 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.615554 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.615555 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.615557 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.615558 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.615559 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.615562 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.615564 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.615565 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.615566 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.615568 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.615569 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.615571 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.615572 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.615574 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.615576 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.615578 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.615579 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.615581 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.615582 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.615584 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.615585 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.615589 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.615590 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.615592 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.615593 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.615595 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.615597 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.615599 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.615600 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.615601 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.615603 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.615604 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.615605 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.615607 79                               Options.ttl: 2592000
2025/08/28-06:43:08.615608 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.615609 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.615611 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.615612 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.615613 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.615615 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.615616 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.615634 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.615638 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.615640 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.615641 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.615642 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.615643 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.629517 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.629525 79           Options.merge_operator: None
2025/08/28-06:43:08.629528 79        Options.compaction_filter: None
2025/08/28-06:43:08.629531 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.629534 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.629536 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.629539 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.629586 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f400ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f400aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.629592 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.629595 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.629598 79          Options.compression: Snappy
2025/08/28-06:43:08.629601 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.629604 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.629607 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.629609 79             Options.num_levels: 7
2025/08/28-06:43:08.629612 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.629615 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.629620 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.629623 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.629626 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.629629 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.629632 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.629635 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.629638 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.629641 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.629647 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.629650 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.629653 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.629656 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.629659 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.629662 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.629665 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.629668 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.629671 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.629673 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.629676 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.629679 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.629682 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.629684 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.629687 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.629690 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.629693 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.629695 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.629708 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.629712 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.629715 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.629717 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.629720 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.629722 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.629725 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.629727 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.629730 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.629733 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.629735 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.629738 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.629740 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.629743 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.629746 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.629764 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.629767 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.629769 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.629772 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.629775 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.629778 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.629780 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.629783 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.629785 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.629789 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.629796 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.629799 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.629801 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.629804 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.629808 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.629810 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.629813 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.629815 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.629817 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.629818 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.629820 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.629821 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.629823 79                               Options.ttl: 2592000
2025/08/28-06:43:08.629824 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.629826 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.629827 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.629829 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.629831 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.629832 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.629834 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.629837 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.629840 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.629844 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.629846 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.629847 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.629849 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.655009 79 DB pointer 0x7f64f4014700
