2025/08/28-06:43:08.536741 83 RocksDB version: 8.1.1
2025/08/28-06:43:08.536969 83 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.536976 83 DB SUMMARY
2025/08/28-06:43:08.536980 83 DB Session ID:  T43XGGKDKN4XM6MRSEDX
2025/08/28-06:43:08.537028 83 SST files in ./storage/collections/example_collection/2/segments/0dc9b23c-c723-4fc4-9030-9df45b54353f dir, Total Num: 0, files: 
2025/08/28-06:43:08.537034 83 Write Ahead Log file in ./storage/collections/example_collection/2/segments/0dc9b23c-c723-4fc4-9030-9df45b54353f: 
2025/08/28-06:43:08.537039 83                         Options.error_if_exists: 0
2025/08/28-06:43:08.537046 83                       Options.create_if_missing: 1
2025/08/28-06:43:08.537050 83                         Options.paranoid_checks: 1
2025/08/28-06:43:08.537056 83             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.537066 83                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.537072 83        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.537077 83                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.537082 83                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.537086 83                                Options.info_log: 0x7f64e4010540
2025/08/28-06:43:08.537090 83                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.537095 83                              Options.statistics: (nil)
2025/08/28-06:43:08.537099 83                               Options.use_fsync: 0
2025/08/28-06:43:08.537104 83                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.537108 83                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.537113 83                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.537116 83                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.537131 83                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.537136 83                         Options.allow_fallocate: 1
2025/08/28-06:43:08.537140 83                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.537144 83                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.537147 83                        Options.use_direct_reads: 0
2025/08/28-06:43:08.537151 83                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.537155 83          Options.create_missing_column_families: 1
2025/08/28-06:43:08.537161 83                              Options.db_log_dir: 
2025/08/28-06:43:08.537165 83                                 Options.wal_dir: 
2025/08/28-06:43:08.537172 83                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.537182 83                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.537184 83                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.537187 83                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.537190 83             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.537192 83                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.537194 83                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.537197 83                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.537202 83                    Options.write_buffer_manager: 0x7f64e400fae0
2025/08/28-06:43:08.537208 83         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.537211 83           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.537215 83                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.537219 83                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.537223 83     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.537231 83                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.537242 83                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.537246 83                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.537252 83                  Options.unordered_write: 0
2025/08/28-06:43:08.537260 83         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.537263 83      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.537267 83             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.537274 83            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.537277 83                               Options.row_cache: None
2025/08/28-06:43:08.537281 83                              Options.wal_filter: None
2025/08/28-06:43:08.537302 83             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.537304 83             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.537312 83             Options.two_write_queues: 0
2025/08/28-06:43:08.537314 83             Options.manual_wal_flush: 0
2025/08/28-06:43:08.537319 83             Options.wal_compression: 0
2025/08/28-06:43:08.537323 83             Options.atomic_flush: 0
2025/08/28-06:43:08.537326 83             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.537333 83                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.537341 83                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.537345 83                 Options.log_readahead_size: 0
2025/08/28-06:43:08.537349 83                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.537353 83                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.537370 83                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.537375 83            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.537385 83             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.537399 83             Options.db_host_id: __hostname__
2025/08/28-06:43:08.537406 83             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.537412 83             Options.max_background_jobs: 2
2025/08/28-06:43:08.537418 83             Options.max_background_compactions: -1
2025/08/28-06:43:08.537422 83             Options.max_subcompactions: 1
2025/08/28-06:43:08.537430 83             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.537436 83           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.537444 83             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.537449 83             Options.max_total_wal_size: 0
2025/08/28-06:43:08.537457 83             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.537461 83                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.537465 83                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.537468 83                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.537472 83                          Options.max_open_files: 256
2025/08/28-06:43:08.537475 83                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.537479 83                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.537483 83                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.537486 83       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.537490 83                  Options.max_background_flushes: -1
2025/08/28-06:43:08.537493 83 Compression algorithms supported:
2025/08/28-06:43:08.537498 83 	kZSTD supported: 0
2025/08/28-06:43:08.537502 83 	kXpressCompression supported: 0
2025/08/28-06:43:08.537506 83 	kBZip2Compression supported: 0
2025/08/28-06:43:08.537510 83 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.537513 83 	kLZ4Compression supported: 0
2025/08/28-06:43:08.537517 83 	kZlibCompression supported: 0
2025/08/28-06:43:08.537523 83 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.537527 83 	kSnappyCompression supported: 1
2025/08/28-06:43:08.537538 83 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.537542 83 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.555045 83               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.555058 83           Options.merge_operator: None
2025/08/28-06:43:08.555062 83        Options.compaction_filter: None
2025/08/28-06:43:08.555073 83        Options.compaction_filter_factory: None
2025/08/28-06:43:08.555077 83  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.555081 83         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.555085 83            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.555170 83            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64e400d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64e400d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.555178 83        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.555182 83  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.555187 83          Options.compression: Snappy
2025/08/28-06:43:08.555191 83                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.555195 83       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.555198 83   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.555201 83             Options.num_levels: 7
2025/08/28-06:43:08.555205 83        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.555208 83     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.555218 83     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.555223 83            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.555227 83                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.555230 83               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.555234 83         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555238 83         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555242 83         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555245 83                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.555249 83         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555253 83         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555256 83            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.555266 83                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.555271 83               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.555275 83         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555278 83         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555282 83         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555286 83         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555289 83                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.555293 83         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555297 83      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.555300 83          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.555310 83              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.555314 83                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.555318 83             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.555322 83                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.555326 83 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.555332 83          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.555338 83 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.555342 83 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.555345 83 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.555349 83 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.555352 83 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.555356 83 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.555359 83 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.555363 83       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.555366 83                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.555370 83   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.555376 83                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.555380 83   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.555384 83   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.555388 83                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.555393 83                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.555402 83                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.555407 83 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.555410 83 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.555413 83 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.555417 83 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.555420 83 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.555425 83 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.555429 83 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.555432 83 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.555447 83                   Options.table_properties_collectors: 
2025/08/28-06:43:08.555451 83                   Options.inplace_update_support: 0
2025/08/28-06:43:08.555458 83                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.555463 83               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.555467 83               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.555472 83   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.555476 83                           Options.bloom_locality: 0
2025/08/28-06:43:08.555479 83                    Options.max_successive_merges: 0
2025/08/28-06:43:08.555483 83                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.555486 83                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.555489 83                Options.force_consistency_checks: 1
2025/08/28-06:43:08.555492 83                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.555495 83                               Options.ttl: 2592000
2025/08/28-06:43:08.555499 83          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.555502 83  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.555505 83    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.555511 83                       Options.enable_blob_files: false
2025/08/28-06:43:08.555515 83                           Options.min_blob_size: 0
2025/08/28-06:43:08.555524 83                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.555528 83                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.555536 83          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.555542 83      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.555546 83 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.555550 83          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.555554 83                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.555558 83 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.582601 83               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.582617 83           Options.merge_operator: None
2025/08/28-06:43:08.582622 83        Options.compaction_filter: None
2025/08/28-06:43:08.582627 83        Options.compaction_filter_factory: None
2025/08/28-06:43:08.582631 83  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.582636 83         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.582641 83            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.582741 83            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64e4003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64e4003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.582748 83        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.582752 83  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.582756 83          Options.compression: Snappy
2025/08/28-06:43:08.582760 83                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.582764 83       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.582768 83   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.582771 83             Options.num_levels: 7
2025/08/28-06:43:08.582775 83        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.582779 83     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.582782 83     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.582787 83            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.582791 83                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.582795 83               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.582799 83         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.582804 83         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.582807 83         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.582811 83                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.582815 83         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.582818 83         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.582822 83            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.582826 83                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.582830 83               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.582834 83         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.582837 83         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.582840 83         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.582845 83         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.582848 83                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.582850 83         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.582853 83      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.582855 83          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.582857 83              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.582860 83                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.582862 83             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.582865 83                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.582867 83 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.582872 83          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.582875 83 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.582878 83 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.582880 83 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.582882 83 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.582884 83 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.582886 83 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.582889 83 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.582891 83       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.582893 83                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.582895 83   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.582898 83                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.582900 83   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.582902 83   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.582905 83                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.582908 83                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.582911 83                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.582913 83 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.582916 83 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.582923 83 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.582929 83 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.582932 83 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.582934 83 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.582937 83 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.582939 83 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.582949 83                   Options.table_properties_collectors: 
2025/08/28-06:43:08.582952 83                   Options.inplace_update_support: 0
2025/08/28-06:43:08.582954 83                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.582958 83               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.582962 83               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.582966 83   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.582970 83                           Options.bloom_locality: 0
2025/08/28-06:43:08.582973 83                    Options.max_successive_merges: 0
2025/08/28-06:43:08.582976 83                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.582979 83                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.582983 83                Options.force_consistency_checks: 1
2025/08/28-06:43:08.582986 83                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.582989 83                               Options.ttl: 2592000
2025/08/28-06:43:08.582992 83          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.582996 83  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.582999 83    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.583003 83                       Options.enable_blob_files: false
2025/08/28-06:43:08.583006 83                           Options.min_blob_size: 0
2025/08/28-06:43:08.583010 83                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.583018 83                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.583021 83          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.583026 83      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.583030 83 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.583034 83          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.583043 83                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.583047 83 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.599273 83               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.599284 83           Options.merge_operator: None
2025/08/28-06:43:08.599287 83        Options.compaction_filter: None
2025/08/28-06:43:08.599291 83        Options.compaction_filter_factory: None
2025/08/28-06:43:08.599294 83  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.599299 83         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.599302 83            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.599363 83            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64e4005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64e40061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.599368 83        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.599371 83  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.599375 83          Options.compression: Snappy
2025/08/28-06:43:08.599378 83                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.599381 83       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.599384 83   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.599387 83             Options.num_levels: 7
2025/08/28-06:43:08.599391 83        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.599394 83     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.599397 83     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.599401 83            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.599404 83                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.599407 83               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.599410 83         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599414 83         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599417 83         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599421 83                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.599424 83         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599427 83         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599431 83            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599434 83                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599437 83               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599441 83         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599444 83         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599448 83         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599451 83         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599454 83                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599457 83         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599460 83      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599464 83          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599467 83              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599472 83                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599475 83             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599478 83                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599481 83 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599486 83          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599491 83 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599495 83 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599498 83 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599501 83 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599504 83 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599508 83 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599511 83 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599515 83       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599518 83                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599521 83   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599524 83                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599527 83   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599530 83   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599533 83                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599537 83                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599542 83                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599545 83 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599548 83 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599552 83 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599555 83 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599604 83 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599609 83 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599613 83 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599616 83 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599621 83                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599625 83                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599628 83                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599632 83               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599637 83               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599640 83   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599644 83                           Options.bloom_locality: 0
2025/08/28-06:43:08.599647 83                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599650 83                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599653 83                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599656 83                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599660 83                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599662 83                               Options.ttl: 2592000
2025/08/28-06:43:08.599665 83          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599667 83  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599669 83    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599670 83                       Options.enable_blob_files: false
2025/08/28-06:43:08.599672 83                           Options.min_blob_size: 0
2025/08/28-06:43:08.599674 83                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599677 83                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599681 83          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599683 83      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599686 83 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599688 83          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599690 83                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599692 83 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.615227 83               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.615237 83           Options.merge_operator: None
2025/08/28-06:43:08.615240 83        Options.compaction_filter: None
2025/08/28-06:43:08.615243 83        Options.compaction_filter_factory: None
2025/08/28-06:43:08.615245 83  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.615248 83         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.615251 83            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.615303 83            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64e4008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64e4008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.615308 83        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.615311 83  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.615315 83          Options.compression: Snappy
2025/08/28-06:43:08.615318 83                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.615321 83       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.615324 83   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.615327 83             Options.num_levels: 7
2025/08/28-06:43:08.615331 83        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.615334 83     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.615337 83     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.615340 83            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.615343 83                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.615346 83               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.615349 83         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615352 83         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615355 83         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615359 83                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.615362 83         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615364 83         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615367 83            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.615370 83                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.615373 83               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.615376 83         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615379 83         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615381 83         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615384 83         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615387 83                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.615390 83         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615393 83      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.615396 83          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.615399 83              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.615402 83                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.615405 83             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.615408 83                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.615411 83 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.615416 83          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.615420 83 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.615429 83 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.615431 83 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.615432 83 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.615433 83 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.615434 83 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.615436 83 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.615437 83       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.615438 83                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.615440 83   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.615441 83                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.615443 83   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.615444 83   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.615445 83                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.615447 83                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.615448 83                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.615450 83 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.615451 83 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.615453 83 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.615455 83 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.615456 83 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.615458 83 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.615461 83 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.615462 83 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.615468 83                   Options.table_properties_collectors: 
2025/08/28-06:43:08.615469 83                   Options.inplace_update_support: 0
2025/08/28-06:43:08.615471 83                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.615473 83               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.615474 83               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.615476 83   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.615477 83                           Options.bloom_locality: 0
2025/08/28-06:43:08.615478 83                    Options.max_successive_merges: 0
2025/08/28-06:43:08.615479 83                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.615480 83                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.615481 83                Options.force_consistency_checks: 1
2025/08/28-06:43:08.615482 83                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.615483 83                               Options.ttl: 2592000
2025/08/28-06:43:08.615484 83          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.615485 83  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.615487 83    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.615488 83                       Options.enable_blob_files: false
2025/08/28-06:43:08.615489 83                           Options.min_blob_size: 0
2025/08/28-06:43:08.615490 83                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.615491 83                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.615493 83          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.615494 83      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.615496 83 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.615497 83          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.615499 83                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.615500 83 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.634888 83               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.634894 83           Options.merge_operator: None
2025/08/28-06:43:08.634897 83        Options.compaction_filter: None
2025/08/28-06:43:08.634898 83        Options.compaction_filter_factory: None
2025/08/28-06:43:08.634900 83  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.634902 83         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.634903 83            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.634937 83            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64e400ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64e400aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.634941 83        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.634943 83  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.634945 83          Options.compression: Snappy
2025/08/28-06:43:08.634947 83                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.634948 83       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.634950 83   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.634951 83             Options.num_levels: 7
2025/08/28-06:43:08.634953 83        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.634955 83     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.634956 83     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.634958 83            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.634960 83                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.634962 83               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.634963 83         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.634965 83         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.634967 83         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.634969 83                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.634970 83         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.634972 83         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.634974 83            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.634975 83                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.634977 83               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.634979 83         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.634980 83         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.634982 83         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.634983 83         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.634985 83                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.634986 83         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.634988 83      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.634990 83          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.634991 83              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.634993 83                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.634994 83             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.634996 83                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.634998 83 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.635001 83          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.635004 83 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.635005 83 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.635007 83 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.635008 83 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.635010 83 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.635011 83 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.635013 83 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.635015 83       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.635016 83                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.635018 83   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.635019 83                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.635021 83   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.635023 83   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.635025 83                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.635027 83                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.635029 83                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.635030 83 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.635032 83 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.635033 83 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.635035 83 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.635037 83 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.635039 83 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.635040 83 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.635042 83 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.635048 83                   Options.table_properties_collectors: 
2025/08/28-06:43:08.635050 83                   Options.inplace_update_support: 0
2025/08/28-06:43:08.635052 83                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.635053 83               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.635055 83               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.635057 83   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.635058 83                           Options.bloom_locality: 0
2025/08/28-06:43:08.635060 83                    Options.max_successive_merges: 0
2025/08/28-06:43:08.635061 83                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.635063 83                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.635064 83                Options.force_consistency_checks: 1
2025/08/28-06:43:08.635066 83                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.635067 83                               Options.ttl: 2592000
2025/08/28-06:43:08.635069 83          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.635070 83  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.635072 83    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.635073 83                       Options.enable_blob_files: false
2025/08/28-06:43:08.635075 83                           Options.min_blob_size: 0
2025/08/28-06:43:08.635076 83                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.635078 83                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.635080 83          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.635082 83      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.635084 83 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.635086 83          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.635087 83                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.635089 83 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.652047 83 DB pointer 0x7f64e4014700
