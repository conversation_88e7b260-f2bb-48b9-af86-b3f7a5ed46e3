2025/08/28-06:43:08.536738 82 RocksDB version: 8.1.1
2025/08/28-06:43:08.536938 82 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.536951 82 DB SUMMARY
2025/08/28-06:43:08.536956 82 DB Session ID:  T43XGGKDKN4XM6MRSEDU
2025/08/28-06:43:08.537002 82 SST files in ./storage/collections/example_collection/2/segments/c682a3c2-b1b8-428b-b2bd-d0891e3a4e24 dir, Total Num: 0, files: 
2025/08/28-06:43:08.537008 82 Write Ahead Log file in ./storage/collections/example_collection/2/segments/c682a3c2-b1b8-428b-b2bd-d0891e3a4e24: 
2025/08/28-06:43:08.537126 82                         Options.error_if_exists: 0
2025/08/28-06:43:08.537135 82                       Options.create_if_missing: 1
2025/08/28-06:43:08.537139 82                         Options.paranoid_checks: 1
2025/08/28-06:43:08.537145 82             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.537149 82                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.537153 82        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.537157 82                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.537160 82                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.537164 82                                Options.info_log: 0x7f64f0010540
2025/08/28-06:43:08.537168 82                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.537172 82                              Options.statistics: (nil)
2025/08/28-06:43:08.537184 82                               Options.use_fsync: 0
2025/08/28-06:43:08.537190 82                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.537194 82                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.537198 82                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.537201 82                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.537205 82                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.537209 82                         Options.allow_fallocate: 1
2025/08/28-06:43:08.537212 82                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.537217 82                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.537222 82                        Options.use_direct_reads: 0
2025/08/28-06:43:08.537226 82                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.537231 82          Options.create_missing_column_families: 1
2025/08/28-06:43:08.537345 82                              Options.db_log_dir: 
2025/08/28-06:43:08.537351 82                                 Options.wal_dir: 
2025/08/28-06:43:08.537376 82                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.537384 82                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.537388 82                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.537393 82                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.537397 82             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.537406 82                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.537424 82                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.537428 82                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.537432 82                    Options.write_buffer_manager: 0x7f64f000fae0
2025/08/28-06:43:08.537438 82         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.537443 82           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.537447 82                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.537450 82                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.537457 82     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.537460 82                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.537465 82                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.537469 82                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.537472 82                  Options.unordered_write: 0
2025/08/28-06:43:08.537481 82         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.537489 82      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.537493 82             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.537499 82            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.537504 82                               Options.row_cache: None
2025/08/28-06:43:08.537508 82                              Options.wal_filter: None
2025/08/28-06:43:08.537514 82             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.537518 82             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.537521 82             Options.two_write_queues: 0
2025/08/28-06:43:08.537525 82             Options.manual_wal_flush: 0
2025/08/28-06:43:08.537530 82             Options.wal_compression: 0
2025/08/28-06:43:08.537535 82             Options.atomic_flush: 0
2025/08/28-06:43:08.537539 82             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.537542 82                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.537550 82                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.537557 82                 Options.log_readahead_size: 0
2025/08/28-06:43:08.537561 82                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.537566 82                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.537569 82                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.537600 82            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.537605 82             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.537609 82             Options.db_host_id: __hostname__
2025/08/28-06:43:08.537612 82             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.537616 82             Options.max_background_jobs: 2
2025/08/28-06:43:08.537619 82             Options.max_background_compactions: -1
2025/08/28-06:43:08.537623 82             Options.max_subcompactions: 1
2025/08/28-06:43:08.537628 82             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.537631 82           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.537783 82             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.537960 82             Options.max_total_wal_size: 0
2025/08/28-06:43:08.537968 82             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.537972 82                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.537976 82                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.537979 82                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.537982 82                          Options.max_open_files: 256
2025/08/28-06:43:08.537989 82                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.537993 82                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.537995 82                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.537998 82       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.538001 82                  Options.max_background_flushes: -1
2025/08/28-06:43:08.538004 82 Compression algorithms supported:
2025/08/28-06:43:08.538008 82 	kZSTD supported: 0
2025/08/28-06:43:08.538014 82 	kXpressCompression supported: 0
2025/08/28-06:43:08.538017 82 	kBZip2Compression supported: 0
2025/08/28-06:43:08.538020 82 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.538023 82 	kLZ4Compression supported: 0
2025/08/28-06:43:08.538026 82 	kZlibCompression supported: 0
2025/08/28-06:43:08.538029 82 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.538032 82 	kSnappyCompression supported: 1
2025/08/28-06:43:08.538037 82 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.538040 82 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.556921 82               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.556932 82           Options.merge_operator: None
2025/08/28-06:43:08.556937 82        Options.compaction_filter: None
2025/08/28-06:43:08.556947 82        Options.compaction_filter_factory: None
2025/08/28-06:43:08.556996 82  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.557001 82         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.557231 82            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.557418 82            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f000d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f000d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.557432 82        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.557436 82  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.557441 82          Options.compression: Snappy
2025/08/28-06:43:08.557445 82                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.557448 82       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.557451 82   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.557454 82             Options.num_levels: 7
2025/08/28-06:43:08.557457 82        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.557460 82     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.557464 82     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.557468 82            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.557472 82                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.557475 82               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.557479 82         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.557482 82         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.557485 82         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.557488 82                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.557492 82         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.557495 82         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.557503 82            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.557507 82                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.557511 82               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.557514 82         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.557517 82         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.557520 82         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.557523 82         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.557526 82                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.557529 82         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.557532 82      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.557535 82          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.557544 82              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.557548 82                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.557551 82             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.557554 82                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.557557 82 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.557563 82          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.557568 82 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.557572 82 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.557575 82 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.557578 82 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.557581 82 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.557584 82 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.557587 82 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.557590 82       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.557593 82                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.557597 82   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.557600 82                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.557603 82   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.557606 82   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.557615 82                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.557620 82                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.557624 82                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.557628 82 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.557631 82 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.557634 82 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.557637 82 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.557640 82 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.557644 82 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.557647 82 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.557650 82 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.557662 82                   Options.table_properties_collectors: 
2025/08/28-06:43:08.557666 82                   Options.inplace_update_support: 0
2025/08/28-06:43:08.557670 82                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.557782 82               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.557789 82               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.557792 82   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.557796 82                           Options.bloom_locality: 0
2025/08/28-06:43:08.557799 82                    Options.max_successive_merges: 0
2025/08/28-06:43:08.557801 82                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.557804 82                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.557807 82                Options.force_consistency_checks: 1
2025/08/28-06:43:08.557810 82                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.557813 82                               Options.ttl: 2592000
2025/08/28-06:43:08.557816 82          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.557818 82  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.557821 82    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.558011 82                       Options.enable_blob_files: false
2025/08/28-06:43:08.558022 82                           Options.min_blob_size: 0
2025/08/28-06:43:08.558038 82                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.558043 82                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.558046 82          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.558052 82      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.558056 82 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.558060 82          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.558063 82                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.558067 82 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.584227 82               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.584233 82           Options.merge_operator: None
2025/08/28-06:43:08.584236 82        Options.compaction_filter: None
2025/08/28-06:43:08.584240 82        Options.compaction_filter_factory: None
2025/08/28-06:43:08.584244 82  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.584248 82         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.584252 82            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.584321 82            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f0003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f0003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.584326 82        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.584330 82  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.584333 82          Options.compression: Snappy
2025/08/28-06:43:08.584338 82                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.584341 82       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.584345 82   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.584349 82             Options.num_levels: 7
2025/08/28-06:43:08.584353 82        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.584356 82     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.584361 82     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.584365 82            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.584369 82                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.584373 82               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.584378 82         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584381 82         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584386 82         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584391 82                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.584394 82         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584398 82         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584401 82            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.584408 82                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.584413 82               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.584417 82         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584420 82         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584423 82         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584427 82         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584430 82                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.584433 82         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584437 82      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.584440 82          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.584444 82              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.584447 82                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.584451 82             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.584454 82                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.584457 82 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.584462 82          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.584466 82 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.584470 82 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.584476 82 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.584480 82 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.584483 82 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.584487 82 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.584490 82 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.584494 82       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.584497 82                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.584501 82   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.584505 82                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.584508 82   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.584512 82   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.584518 82                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.584522 82                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.584530 82                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.584534 82 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.584538 82 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.584541 82 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.584545 82 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.584549 82 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.584552 82 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.584556 82 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.584560 82 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.584572 82                   Options.table_properties_collectors: 
2025/08/28-06:43:08.584577 82                   Options.inplace_update_support: 0
2025/08/28-06:43:08.584581 82                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.584586 82               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.584591 82               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.584595 82   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.584599 82                           Options.bloom_locality: 0
2025/08/28-06:43:08.584603 82                    Options.max_successive_merges: 0
2025/08/28-06:43:08.584607 82                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.584612 82                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.584616 82                Options.force_consistency_checks: 1
2025/08/28-06:43:08.584622 82                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.584627 82                               Options.ttl: 2592000
2025/08/28-06:43:08.584633 82          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.584637 82  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.584641 82    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.584645 82                       Options.enable_blob_files: false
2025/08/28-06:43:08.584648 82                           Options.min_blob_size: 0
2025/08/28-06:43:08.584652 82                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.584656 82                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.584665 82          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.584670 82      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.584675 82 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.584679 82          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.584683 82                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.584687 82 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601800 82               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601808 82           Options.merge_operator: None
2025/08/28-06:43:08.601812 82        Options.compaction_filter: None
2025/08/28-06:43:08.601815 82        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601818 82  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601822 82         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601825 82            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.601874 82            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f0005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f00061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.601882 82        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.601886 82  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.601890 82          Options.compression: Snappy
2025/08/28-06:43:08.601893 82                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.601897 82       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.601900 82   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.601903 82             Options.num_levels: 7
2025/08/28-06:43:08.601907 82        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.601910 82     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.601914 82     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.601917 82            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.601921 82                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.601924 82               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.601928 82         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601931 82         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601935 82         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601938 82                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.601942 82         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601945 82         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601948 82            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.601952 82                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.601957 82               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.601960 82         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601963 82         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601966 82         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601970 82         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601973 82                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.601976 82         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601980 82      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.601983 82          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.601987 82              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.601990 82                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.601993 82             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.601996 82                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602003 82 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602008 82          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602013 82 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602017 82 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602020 82 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602026 82 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602029 82 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602032 82 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602035 82 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602038 82       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602044 82                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602047 82   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602052 82                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602056 82   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602059 82   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602062 82                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602066 82                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602071 82                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602074 82 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602077 82 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602082 82 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602085 82 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602089 82 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602092 82 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602096 82 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602099 82 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602108 82                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602112 82                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602115 82                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602118 82               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602122 82               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602125 82   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602129 82                           Options.bloom_locality: 0
2025/08/28-06:43:08.602132 82                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602135 82                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602140 82                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602143 82                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602146 82                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602152 82                               Options.ttl: 2592000
2025/08/28-06:43:08.602155 82          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602158 82  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602161 82    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602164 82                       Options.enable_blob_files: false
2025/08/28-06:43:08.602167 82                           Options.min_blob_size: 0
2025/08/28-06:43:08.602170 82                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602174 82                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602177 82          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602182 82      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602186 82 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602192 82          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602196 82                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602200 82 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.617345 82               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.617357 82           Options.merge_operator: None
2025/08/28-06:43:08.617360 82        Options.compaction_filter: None
2025/08/28-06:43:08.617363 82        Options.compaction_filter_factory: None
2025/08/28-06:43:08.617366 82  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.617369 82         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.617373 82            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.617435 82            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f0008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f0008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.617441 82        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.617445 82  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.617450 82          Options.compression: Snappy
2025/08/28-06:43:08.617453 82                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.617457 82       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.617465 82   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.617469 82             Options.num_levels: 7
2025/08/28-06:43:08.617473 82        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.617477 82     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.617481 82     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.617485 82            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.617489 82                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.617492 82               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.617498 82         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617502 82         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617506 82         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617510 82                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.617513 82         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617517 82         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617521 82            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.617525 82                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.617529 82               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.617532 82         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617536 82         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617539 82         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617542 82         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617545 82                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.617609 82         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617612 82      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.617616 82          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.617619 82              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.617622 82                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.617625 82             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.617631 82                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.617634 82 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.617639 82          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.617644 82 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.617647 82 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.617650 82 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.617656 82 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.617660 82 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.617663 82 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.617895 82 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.617897 82       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.617898 82                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.617900 82   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.617901 82                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.617902 82   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.617904 82   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.617905 82                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.617906 82                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.617908 82                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.617909 82 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.617911 82 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.617912 82 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.617913 82 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.617915 82 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.617916 82 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.617918 82 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.617920 82 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.617923 82                   Options.table_properties_collectors: 
2025/08/28-06:43:08.617924 82                   Options.inplace_update_support: 0
2025/08/28-06:43:08.617926 82                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.617928 82               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.617930 82               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.617931 82   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.617933 82                           Options.bloom_locality: 0
2025/08/28-06:43:08.617935 82                    Options.max_successive_merges: 0
2025/08/28-06:43:08.617936 82                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.617938 82                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.617939 82                Options.force_consistency_checks: 1
2025/08/28-06:43:08.617940 82                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.617942 82                               Options.ttl: 2592000
2025/08/28-06:43:08.617943 82          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.617944 82  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.617947 82    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.617948 82                       Options.enable_blob_files: false
2025/08/28-06:43:08.617951 82                           Options.min_blob_size: 0
2025/08/28-06:43:08.617952 82                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.617954 82                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.617955 82          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.617957 82      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.617960 82 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.617962 82          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.617963 82                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.617965 82 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637907 82               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637916 82           Options.merge_operator: None
2025/08/28-06:43:08.637923 82        Options.compaction_filter: None
2025/08/28-06:43:08.637928 82        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637933 82  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637937 82         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637942 82            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637991 82            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64f000ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64f000aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637997 82        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.638000 82  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.638003 82          Options.compression: Snappy
2025/08/28-06:43:08.638005 82                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.638006 82       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.638024 82   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.638027 82             Options.num_levels: 7
2025/08/28-06:43:08.638031 82        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.638033 82     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.638035 82     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.638037 82            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.638040 82                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.638042 82               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.638044 82         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638046 82         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638048 82         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638050 82                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.638051 82         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638053 82         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638056 82            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.638058 82                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.638060 82               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.638062 82         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638064 82         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638066 82         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638068 82         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638070 82                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.638072 82         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638074 82      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.638076 82          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.638078 82              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.638080 82                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.638082 82             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.638085 82                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.638087 82 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.638091 82          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.638094 82 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.638097 82 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.638099 82 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.638104 82 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.638105 82 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.638107 82 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.638109 82 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.638110 82       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.638112 82                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.638115 82   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.638117 82                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.638119 82   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.638120 82   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.638122 82                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.638123 82                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.638125 82                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.638126 82 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.638128 82 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.638129 82 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.638131 82 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.638132 82 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.638134 82 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638135 82 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638136 82 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638140 82                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638141 82                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638143 82                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638144 82               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638146 82               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638148 82   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638149 82                           Options.bloom_locality: 0
2025/08/28-06:43:08.638150 82                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638151 82                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638153 82                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638154 82                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638155 82                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638157 82                               Options.ttl: 2592000
2025/08/28-06:43:08.638158 82          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638159 82  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638160 82    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638162 82                       Options.enable_blob_files: false
2025/08/28-06:43:08.638163 82                           Options.min_blob_size: 0
2025/08/28-06:43:08.638164 82                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638165 82                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638167 82          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638169 82      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638171 82 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638172 82          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638173 82                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638175 82 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.658914 82 DB pointer 0x7f64f0014700
