2025/08/28-06:43:08.536754 81 RocksDB version: 8.1.1
2025/08/28-06:43:08.536982 81 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.536989 81 DB SUMMARY
2025/08/28-06:43:08.537037 81 DB Session ID:  T43XGGKDKN4XM6MRSEDW
2025/08/28-06:43:08.537102 81 SST files in ./storage/collections/example_collection/2/segments/15339008-3cac-43a5-ba57-5dee17629f31 dir, Total Num: 0, files: 
2025/08/28-06:43:08.537112 81 Write Ahead Log file in ./storage/collections/example_collection/2/segments/15339008-3cac-43a5-ba57-5dee17629f31: 
2025/08/28-06:43:08.537119 81                         Options.error_if_exists: 0
2025/08/28-06:43:08.537123 81                       Options.create_if_missing: 1
2025/08/28-06:43:08.537127 81                         Options.paranoid_checks: 1
2025/08/28-06:43:08.537131 81             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.537141 81                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.537145 81        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.537150 81                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.537154 81                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.537195 81                                Options.info_log: 0x7f64ec010540
2025/08/28-06:43:08.537202 81                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.537207 81                              Options.statistics: (nil)
2025/08/28-06:43:08.537212 81                               Options.use_fsync: 0
2025/08/28-06:43:08.537216 81                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.537223 81                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.537227 81                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.537232 81                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.537240 81                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.537244 81                         Options.allow_fallocate: 1
2025/08/28-06:43:08.537248 81                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.537256 81                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.537260 81                        Options.use_direct_reads: 0
2025/08/28-06:43:08.537263 81                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.537267 81          Options.create_missing_column_families: 1
2025/08/28-06:43:08.537277 81                              Options.db_log_dir: 
2025/08/28-06:43:08.537280 81                                 Options.wal_dir: 
2025/08/28-06:43:08.537289 81                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.537293 81                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.537297 81                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.537301 81                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.537305 81             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.537309 81                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.537314 81                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.537318 81                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.537322 81                    Options.write_buffer_manager: 0x7f64ec00fae0
2025/08/28-06:43:08.537332 81         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.537346 81           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.537351 81                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.537367 81                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.537372 81     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.537375 81                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.537399 81                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.537405 81                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.537409 81                  Options.unordered_write: 0
2025/08/28-06:43:08.537417 81         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.537422 81      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.537436 81             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.537449 81            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.537456 81                               Options.row_cache: None
2025/08/28-06:43:08.537460 81                              Options.wal_filter: None
2025/08/28-06:43:08.537465 81             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.537468 81             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.537472 81             Options.two_write_queues: 0
2025/08/28-06:43:08.537479 81             Options.manual_wal_flush: 0
2025/08/28-06:43:08.537487 81             Options.wal_compression: 0
2025/08/28-06:43:08.537491 81             Options.atomic_flush: 0
2025/08/28-06:43:08.537494 81             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.537500 81                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.537504 81                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.537508 81                 Options.log_readahead_size: 0
2025/08/28-06:43:08.537513 81                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.537517 81                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.537522 81                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.537526 81            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.537532 81             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.537536 81             Options.db_host_id: __hostname__
2025/08/28-06:43:08.537544 81             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.537549 81             Options.max_background_jobs: 2
2025/08/28-06:43:08.537558 81             Options.max_background_compactions: -1
2025/08/28-06:43:08.537564 81             Options.max_subcompactions: 1
2025/08/28-06:43:08.537574 81             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.537580 81           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.537584 81             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.537591 81             Options.max_total_wal_size: 0
2025/08/28-06:43:08.537595 81             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.537602 81                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.537606 81                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.537610 81                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.537613 81                          Options.max_open_files: 256
2025/08/28-06:43:08.537617 81                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.537621 81                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.537625 81                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.537630 81       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.537634 81                  Options.max_background_flushes: -1
2025/08/28-06:43:08.537638 81 Compression algorithms supported:
2025/08/28-06:43:08.537643 81 	kZSTD supported: 0
2025/08/28-06:43:08.537648 81 	kXpressCompression supported: 0
2025/08/28-06:43:08.537653 81 	kBZip2Compression supported: 0
2025/08/28-06:43:08.537674 81 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.537681 81 	kLZ4Compression supported: 0
2025/08/28-06:43:08.537686 81 	kZlibCompression supported: 0
2025/08/28-06:43:08.537690 81 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.537694 81 	kSnappyCompression supported: 1
2025/08/28-06:43:08.537713 81 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.537717 81 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.555095 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.555106 81           Options.merge_operator: None
2025/08/28-06:43:08.555111 81        Options.compaction_filter: None
2025/08/28-06:43:08.555121 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.555125 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.555129 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.555132 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.555208 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64ec00d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64ec00d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.555220 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.555225 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.555229 81          Options.compression: Snappy
2025/08/28-06:43:08.555233 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.555236 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.555240 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.555243 81             Options.num_levels: 7
2025/08/28-06:43:08.555247 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.555251 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.555254 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.555258 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.555262 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.555266 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.555269 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555273 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555276 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555280 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.555284 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555287 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555291 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.555294 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.555304 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.555309 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.555312 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.555316 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.555319 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.555323 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.555326 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.555330 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.555333 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.555343 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.555347 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.555350 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.555354 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.555358 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.555364 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.555370 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.555378 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.555382 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.555385 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.555389 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.555392 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.555395 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.555398 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.555401 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.555404 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.555407 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.555411 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.555414 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.555417 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.555421 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.555430 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.555435 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.555438 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.555441 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.555444 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.555447 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.555451 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.555456 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.555460 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.555467 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.555474 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.555478 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.555482 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.555486 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.555490 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.555493 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.555496 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.555499 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.555502 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.555506 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.555512 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.555516 81                               Options.ttl: 2592000
2025/08/28-06:43:08.555519 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.555522 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.555525 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.555528 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.555531 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.555540 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.555579 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.555589 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.555595 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.555599 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.555603 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.555606 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.555610 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.578583 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.578600 81           Options.merge_operator: None
2025/08/28-06:43:08.578605 81        Options.compaction_filter: None
2025/08/28-06:43:08.578610 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.578615 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.578620 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.578624 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.578765 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64ec003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64ec003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.578777 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.578783 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.578787 81          Options.compression: Snappy
2025/08/28-06:43:08.578790 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.578793 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.578795 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.578798 81             Options.num_levels: 7
2025/08/28-06:43:08.578802 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.578804 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.578807 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.578810 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.578814 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.578818 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.578822 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.578827 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.578831 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.578836 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.578840 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.578845 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.578850 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.578855 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.578859 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.578862 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.578865 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.578868 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.578871 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.578874 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.578877 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.578887 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.578891 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.578895 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.578900 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.578904 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.578908 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.578912 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.578920 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.578925 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.578930 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.578937 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.578942 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.578946 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.578950 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.578954 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.578957 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.578962 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.578966 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.578970 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.578974 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.578979 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.578987 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.578993 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.578998 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.579002 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.579007 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.579011 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.579015 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.579020 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.579024 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.579029 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.579033 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.579048 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.579051 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.579053 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.579057 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.579060 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.579063 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.579066 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.579069 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.579072 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.579074 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.579076 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.579079 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.579081 81                               Options.ttl: 2592000
2025/08/28-06:43:08.579084 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.579086 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.579088 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.579091 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.579094 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.579101 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.579106 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.579110 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.579115 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.579119 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.579123 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.579127 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.579132 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.599275 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.599282 81           Options.merge_operator: None
2025/08/28-06:43:08.599288 81        Options.compaction_filter: None
2025/08/28-06:43:08.599293 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.599296 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.599300 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.599303 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.599363 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64ec005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64ec0061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.599370 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.599373 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.599376 81          Options.compression: Snappy
2025/08/28-06:43:08.599379 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.599382 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.599384 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.599387 81             Options.num_levels: 7
2025/08/28-06:43:08.599390 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.599393 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.599396 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.599399 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.599402 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.599405 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.599408 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599411 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599414 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599416 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.599420 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599423 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599426 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599429 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599432 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599435 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599437 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599443 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599447 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599449 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599452 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599455 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599459 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599461 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599464 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599467 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599472 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599476 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599480 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599485 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599488 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599491 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599496 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599498 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599501 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599504 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599509 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599513 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599516 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599519 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599521 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599525 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599527 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599531 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599534 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599537 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599543 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599546 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599549 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599552 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599556 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599559 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599562 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599570 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599574 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599577 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599581 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599585 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599588 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599591 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.599594 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599596 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599600 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599603 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599605 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599609 81                               Options.ttl: 2592000
2025/08/28-06:43:08.599611 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599614 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599617 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599620 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.599650 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.599654 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599658 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599661 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599665 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599669 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599673 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599676 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599683 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.617519 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.617527 81           Options.merge_operator: None
2025/08/28-06:43:08.617532 81        Options.compaction_filter: None
2025/08/28-06:43:08.617535 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.617538 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.617541 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.617544 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.617635 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64ec008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64ec008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.617641 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.617648 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.617651 81          Options.compression: Snappy
2025/08/28-06:43:08.617654 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.617657 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.617660 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.617662 81             Options.num_levels: 7
2025/08/28-06:43:08.617678 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.617683 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.617684 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.617686 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.617687 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.617689 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.617690 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617692 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617693 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617695 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.617696 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617698 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617699 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.617710 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.617712 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.617713 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617715 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617716 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617718 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617719 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.617721 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617722 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.617724 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.617725 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.617726 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.617728 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.617729 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.617731 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.617733 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.617735 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.617737 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.617738 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.617740 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.617741 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.617742 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.617743 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.617745 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.617746 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.617748 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.617749 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.617750 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.617752 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.617753 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.617755 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.617756 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.617758 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.617759 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.617761 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.617762 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.617764 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.617765 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.617766 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.617768 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.617772 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.617774 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.617775 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.617777 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.617778 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.617780 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.617781 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.617783 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.617784 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.617785 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.617786 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.617788 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.617789 81                               Options.ttl: 2592000
2025/08/28-06:43:08.617791 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.617792 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.617793 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.617795 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.617796 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.617797 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.617799 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.617800 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.617802 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.617804 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.617806 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.617807 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.617809 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637747 81               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637755 81           Options.merge_operator: None
2025/08/28-06:43:08.637757 81        Options.compaction_filter: None
2025/08/28-06:43:08.637760 81        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637763 81  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637766 81         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637770 81            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637808 81            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f64ec00ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f64ec00aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637812 81        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637815 81  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637819 81          Options.compression: Snappy
2025/08/28-06:43:08.637821 81                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637824 81       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637826 81   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637829 81             Options.num_levels: 7
2025/08/28-06:43:08.637832 81        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637834 81     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637837 81     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637839 81            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.637842 81                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.637845 81               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.637847 81         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637850 81         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637853 81         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637855 81                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.637858 81         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637861 81         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637864 81            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.637866 81                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.637869 81               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.637872 81         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637874 81         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637878 81         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637880 81         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637883 81                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.637887 81         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637890 81      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.637893 81          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.637897 81              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.637900 81                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.637903 81             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.637906 81                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.637909 81 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.637913 81          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.637916 81 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.637921 81 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.637923 81 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.637927 81 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.637930 81 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.637935 81 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.637937 81 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.637941 81       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.637943 81                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.637946 81   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.637948 81                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.637951 81   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.637954 81   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.637956 81                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.637959 81                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.637962 81                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.637968 81 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.637972 81 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.637974 81 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.637976 81 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.637980 81 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.637984 81 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.637987 81 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.637989 81 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.637996 81                   Options.table_properties_collectors: 
2025/08/28-06:43:08.637999 81                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638004 81                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638007 81               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638009 81               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638010 81   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638011 81                           Options.bloom_locality: 0
2025/08/28-06:43:08.638012 81                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638014 81                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638015 81                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638016 81                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638018 81                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638019 81                               Options.ttl: 2592000
2025/08/28-06:43:08.638020 81          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638022 81  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638023 81    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638025 81                       Options.enable_blob_files: false
2025/08/28-06:43:08.638026 81                           Options.min_blob_size: 0
2025/08/28-06:43:08.638027 81                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638029 81                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638031 81          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638032 81      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638034 81 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638036 81          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638037 81                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638039 81 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.658118 81 DB pointer 0x7f64ec014700
