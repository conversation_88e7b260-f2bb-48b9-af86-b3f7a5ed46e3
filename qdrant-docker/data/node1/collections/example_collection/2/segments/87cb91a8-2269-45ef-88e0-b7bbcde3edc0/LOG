2025/08/28-06:43:08.536782 76 RocksDB version: 8.1.1
2025/08/28-06:43:08.537222 76 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.537231 76 DB SUMMARY
2025/08/28-06:43:08.537241 76 DB Session ID:  T43XGGKDKN4XM6MRSEDV
2025/08/28-06:43:08.537272 76 SST files in ./storage/collections/example_collection/2/segments/87cb91a8-2269-45ef-88e0-b7bbcde3edc0 dir, Total Num: 0, files: 
2025/08/28-06:43:08.537277 76 Write Ahead Log file in ./storage/collections/example_collection/2/segments/87cb91a8-2269-45ef-88e0-b7bbcde3edc0: 
2025/08/28-06:43:08.537281 76                         Options.error_if_exists: 0
2025/08/28-06:43:08.537287 76                       Options.create_if_missing: 1
2025/08/28-06:43:08.537290 76                         Options.paranoid_checks: 1
2025/08/28-06:43:08.537294 76             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.537298 76                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.537304 76        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.537313 76                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.537318 76                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.537322 76                                Options.info_log: 0x7f653c012c40
2025/08/28-06:43:08.537326 76                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.537332 76                              Options.statistics: (nil)
2025/08/28-06:43:08.537336 76                               Options.use_fsync: 0
2025/08/28-06:43:08.537392 76                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.537400 76                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.537406 76                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.537418 76                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.537424 76                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.537429 76                         Options.allow_fallocate: 1
2025/08/28-06:43:08.537433 76                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.537437 76                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.537465 76                        Options.use_direct_reads: 0
2025/08/28-06:43:08.537469 76                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.537473 76          Options.create_missing_column_families: 1
2025/08/28-06:43:08.537476 76                              Options.db_log_dir: 
2025/08/28-06:43:08.537480 76                                 Options.wal_dir: 
2025/08/28-06:43:08.537487 76                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.537490 76                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.537494 76                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.537498 76                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.537503 76             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.537506 76                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.537510 76                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.537515 76                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.537519 76                    Options.write_buffer_manager: 0x7f653c0121e0
2025/08/28-06:43:08.537524 76         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.537529 76           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.537534 76                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.537537 76                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.537541 76     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.537559 76                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.537564 76                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.537571 76                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.537574 76                  Options.unordered_write: 0
2025/08/28-06:43:08.537581 76         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.537584 76      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.537588 76             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.537593 76            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.537596 76                               Options.row_cache: None
2025/08/28-06:43:08.537599 76                              Options.wal_filter: None
2025/08/28-06:43:08.537604 76             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.537609 76             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.537612 76             Options.two_write_queues: 0
2025/08/28-06:43:08.537622 76             Options.manual_wal_flush: 0
2025/08/28-06:43:08.537625 76             Options.wal_compression: 0
2025/08/28-06:43:08.537631 76             Options.atomic_flush: 0
2025/08/28-06:43:08.537649 76             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.537653 76                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.537661 76                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.537669 76                 Options.log_readahead_size: 0
2025/08/28-06:43:08.537673 76                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.537677 76                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.537680 76                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.537696 76            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.537712 76             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.537716 76             Options.db_host_id: __hostname__
2025/08/28-06:43:08.537720 76             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.537723 76             Options.max_background_jobs: 2
2025/08/28-06:43:08.537727 76             Options.max_background_compactions: -1
2025/08/28-06:43:08.537730 76             Options.max_subcompactions: 1
2025/08/28-06:43:08.537734 76             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.537738 76           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.537742 76             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.537747 76             Options.max_total_wal_size: 0
2025/08/28-06:43:08.537756 76             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.537761 76                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.537768 76                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.537771 76                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.537778 76                          Options.max_open_files: 256
2025/08/28-06:43:08.537783 76                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.537786 76                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.537790 76                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.537793 76       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.537796 76                  Options.max_background_flushes: -1
2025/08/28-06:43:08.537806 76 Compression algorithms supported:
2025/08/28-06:43:08.537810 76 	kZSTD supported: 0
2025/08/28-06:43:08.537815 76 	kXpressCompression supported: 0
2025/08/28-06:43:08.537820 76 	kBZip2Compression supported: 0
2025/08/28-06:43:08.537825 76 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.537829 76 	kLZ4Compression supported: 0
2025/08/28-06:43:08.537833 76 	kZlibCompression supported: 0
2025/08/28-06:43:08.537838 76 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.537843 76 	kSnappyCompression supported: 1
2025/08/28-06:43:08.537851 76 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.537857 76 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.556413 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.556421 76           Options.merge_operator: None
2025/08/28-06:43:08.556426 76        Options.compaction_filter: None
2025/08/28-06:43:08.556441 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.556445 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.556450 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.556454 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.556537 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f653c00f900)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f653c00fc30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.556543 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.556547 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.556552 76          Options.compression: Snappy
2025/08/28-06:43:08.556556 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.556560 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.556565 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.556570 76             Options.num_levels: 7
2025/08/28-06:43:08.556574 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.556578 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.556583 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.556591 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.556595 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.556599 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.556603 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556611 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556615 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556621 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.556625 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556630 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556634 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.556638 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.556642 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.556645 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556651 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556658 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556662 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556667 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.556671 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556674 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.556680 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.556700 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.556724 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.556753 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.556758 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.556766 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.556793 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.556798 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.556801 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.556810 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.556816 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.556820 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.556825 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.556830 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.556834 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.556838 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.556846 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.556851 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.556855 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.556860 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.556864 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.556871 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.556876 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.556879 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.556884 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.556888 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.556894 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.556898 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.556903 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.556908 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.556912 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.556924 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.556929 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.556934 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.556939 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.556946 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.556951 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.556960 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.556966 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.556970 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.556975 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.556982 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.556986 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.556993 76                               Options.ttl: 2592000
2025/08/28-06:43:08.556997 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.557002 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.557007 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.557012 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.557016 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.557025 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.557030 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.557034 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.557040 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.557045 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.557049 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.557053 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.557057 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.584355 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.584366 76           Options.merge_operator: None
2025/08/28-06:43:08.584370 76        Options.compaction_filter: None
2025/08/28-06:43:08.584378 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.584383 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.584386 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.584393 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.584464 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f653c005f20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f653c006250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.584470 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.584477 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.584481 76          Options.compression: Snappy
2025/08/28-06:43:08.584485 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.584487 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.584490 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.584493 76             Options.num_levels: 7
2025/08/28-06:43:08.584496 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.584499 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.584502 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.584505 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.584509 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.584512 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.584519 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584522 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584526 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584530 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.584536 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584540 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584543 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.584546 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.584549 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.584552 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.584555 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.584560 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.584571 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.584575 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.584578 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.584584 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.584588 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.584591 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.584596 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.584599 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.584602 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.584605 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.584610 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.584615 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.584618 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.584621 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.584628 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.584631 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.584634 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.584637 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.584639 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.584642 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.584646 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.584649 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.584652 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.584655 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.584658 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.584662 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.584669 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.584672 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.584675 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.584678 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.584686 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.584691 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.584694 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.584697 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.584721 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.584734 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.584738 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.584741 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.584744 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.584748 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.584752 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.584754 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.584757 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.584760 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.584762 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.584765 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.584768 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.584771 76                               Options.ttl: 2592000
2025/08/28-06:43:08.584774 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.584777 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.584779 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.584782 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.584785 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.584788 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.584791 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.584793 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.584797 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.584801 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.584805 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.584813 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.584818 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601802 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601809 76           Options.merge_operator: None
2025/08/28-06:43:08.601812 76        Options.compaction_filter: None
2025/08/28-06:43:08.601816 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601820 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601824 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601827 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.601882 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f653c008580)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f653c0088b0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.601887 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.601891 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.601894 76          Options.compression: Snappy
2025/08/28-06:43:08.601898 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.601901 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.601904 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.601907 76             Options.num_levels: 7
2025/08/28-06:43:08.601911 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.601915 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.601918 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.601922 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.601925 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.601929 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.601932 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601935 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601939 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601942 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.601946 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601949 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601952 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.601965 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.601969 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.601972 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601976 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601979 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601982 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601987 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.601990 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601994 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.601997 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.602004 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.602007 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.602010 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.602014 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602017 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602022 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602027 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602030 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602035 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602038 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602043 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602047 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602050 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602053 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602056 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602060 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602063 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602067 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602070 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602073 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602077 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602082 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602088 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602092 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602095 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602099 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602102 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602105 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602110 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602113 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602121 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602124 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602128 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602132 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602135 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602139 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602143 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.602145 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602168 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602172 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602175 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602178 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602181 76                               Options.ttl: 2592000
2025/08/28-06:43:08.602184 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602186 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602190 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602193 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.602196 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.602200 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602206 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602209 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602212 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602215 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602218 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602221 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602225 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.617352 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.617364 76           Options.merge_operator: None
2025/08/28-06:43:08.617367 76        Options.compaction_filter: None
2025/08/28-06:43:08.617370 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.617373 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.617377 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.617380 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.617443 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f653c00ac00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f653c00af30
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.617449 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.617452 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.617457 76          Options.compression: Snappy
2025/08/28-06:43:08.617463 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.617466 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.617469 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.617472 76             Options.num_levels: 7
2025/08/28-06:43:08.617476 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.617480 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.617484 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.617488 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.617492 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.617496 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.617499 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617503 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617506 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617510 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.617514 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617517 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617520 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.617523 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.617527 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.617530 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617533 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617537 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617540 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617544 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.617561 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617565 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.617569 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.617573 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.617576 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.617580 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.617583 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.617587 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.617592 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.617597 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.617601 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.617605 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.617608 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.617612 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.617615 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.617618 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.617622 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.617625 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.617630 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.617633 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.617637 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.617641 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.617645 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.617650 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.617656 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.617662 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.617669 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.617673 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.617676 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.617684 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.617685 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.617686 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.617688 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.617693 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.617694 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.617695 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.617697 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.617699 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.617705 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.617924 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.617926 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.617927 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.617928 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.617930 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.617932 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.617933 76                               Options.ttl: 2592000
2025/08/28-06:43:08.617936 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.617937 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.617938 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.617940 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.617941 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.617943 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.617944 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.617946 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.617949 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.617952 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.617953 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.617955 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.617957 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637783 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637788 76           Options.merge_operator: None
2025/08/28-06:43:08.637790 76        Options.compaction_filter: None
2025/08/28-06:43:08.637793 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637795 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637798 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637800 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637835 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f653c00d280)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f653c00d5b0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637838 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637841 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637844 76          Options.compression: Snappy
2025/08/28-06:43:08.637848 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637850 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637853 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637855 76             Options.num_levels: 7
2025/08/28-06:43:08.637858 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637861 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637864 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637866 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.637869 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.637871 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.637874 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637878 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637880 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637883 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.637887 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637889 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637892 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.637895 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.637897 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.637899 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637902 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637904 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637907 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637909 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.637913 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637915 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.637918 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.637920 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.637923 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.637925 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.637927 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.637930 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.637940 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.637943 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.637946 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.637948 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.637950 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.637953 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.637955 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.637958 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.637960 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.637962 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.637966 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.637969 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.637971 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.637974 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.637976 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.638214 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.638216 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.638219 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.638221 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.638223 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.638225 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.638228 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.638230 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638232 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638234 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638239 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638241 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638244 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638246 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638249 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638251 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638252 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.638254 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638257 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638258 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638260 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638261 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638262 76                               Options.ttl: 2592000
2025/08/28-06:43:08.638264 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638265 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638266 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638267 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.638269 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.638270 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638272 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638273 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638275 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638277 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638278 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638280 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638281 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.659095 76 DB pointer 0x7f653c016e00
