2025/08/28-06:43:08.536738 78 RocksDB version: 8.1.1
2025/08/28-06:43:08.536950 78 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.536955 78 DB SUMMARY
2025/08/28-06:43:08.536959 78 DB Session ID:  T43XGGKDKN4XM6MRSEDZ
2025/08/28-06:43:08.537003 78 SST files in ./storage/collections/example_collection/2/segments/ef09cfcb-0905-414d-aa31-2f673b84c3ff dir, Total Num: 0, files: 
2025/08/28-06:43:08.537009 78 Write Ahead Log file in ./storage/collections/example_collection/2/segments/ef09cfcb-0905-414d-aa31-2f673b84c3ff: 
2025/08/28-06:43:08.537024 78                         Options.error_if_exists: 0
2025/08/28-06:43:08.537029 78                       Options.create_if_missing: 1
2025/08/28-06:43:08.537033 78                         Options.paranoid_checks: 1
2025/08/28-06:43:08.537037 78             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.537045 78                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.537050 78        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.537057 78                                     Options.env: 0x5625ecee99c0
2025/08/28-06:43:08.537062 78                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.537069 78                                Options.info_log: 0x7f6500010540
2025/08/28-06:43:08.537073 78                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.537078 78                              Options.statistics: (nil)
2025/08/28-06:43:08.537083 78                               Options.use_fsync: 0
2025/08/28-06:43:08.537087 78                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.537092 78                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.537097 78                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.537102 78                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.537106 78                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.537111 78                         Options.allow_fallocate: 1
2025/08/28-06:43:08.537115 78                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.537139 78                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.537165 78                        Options.use_direct_reads: 0
2025/08/28-06:43:08.537172 78                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.537350 78          Options.create_missing_column_families: 1
2025/08/28-06:43:08.537378 78                              Options.db_log_dir: 
2025/08/28-06:43:08.537386 78                                 Options.wal_dir: 
2025/08/28-06:43:08.537416 78                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.537421 78                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.537431 78                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.537435 78                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.537439 78             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.537444 78                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.537448 78                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.537452 78                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.537459 78                    Options.write_buffer_manager: 0x7f650000fae0
2025/08/28-06:43:08.537463 78         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.537468 78           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.537471 78                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.537475 78                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.537480 78     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.537502 78                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.537506 78                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.537509 78                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.537515 78                  Options.unordered_write: 0
2025/08/28-06:43:08.537523 78         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.537526 78      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.537532 78             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.537535 78            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.537541 78                               Options.row_cache: None
2025/08/28-06:43:08.537546 78                              Options.wal_filter: None
2025/08/28-06:43:08.537549 78             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.537558 78             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.537564 78             Options.two_write_queues: 0
2025/08/28-06:43:08.537582 78             Options.manual_wal_flush: 0
2025/08/28-06:43:08.537585 78             Options.wal_compression: 0
2025/08/28-06:43:08.537589 78             Options.atomic_flush: 0
2025/08/28-06:43:08.537595 78             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.537600 78                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.537604 78                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.537607 78                 Options.log_readahead_size: 0
2025/08/28-06:43:08.537611 78                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.537618 78                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.537622 78                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.537626 78            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.537631 78             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.537641 78             Options.db_host_id: __hostname__
2025/08/28-06:43:08.537646 78             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.537651 78             Options.max_background_jobs: 2
2025/08/28-06:43:08.537655 78             Options.max_background_compactions: -1
2025/08/28-06:43:08.537662 78             Options.max_subcompactions: 1
2025/08/28-06:43:08.537667 78             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.537670 78           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.537674 78             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.537680 78             Options.max_total_wal_size: 0
2025/08/28-06:43:08.537686 78             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.537690 78                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.537694 78                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.537698 78                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.537717 78                          Options.max_open_files: 256
2025/08/28-06:43:08.537720 78                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.537723 78                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.537727 78                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.537730 78       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.537735 78                  Options.max_background_flushes: -1
2025/08/28-06:43:08.537738 78 Compression algorithms supported:
2025/08/28-06:43:08.537743 78 	kZSTD supported: 0
2025/08/28-06:43:08.537747 78 	kXpressCompression supported: 0
2025/08/28-06:43:08.537755 78 	kBZip2Compression supported: 0
2025/08/28-06:43:08.537759 78 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.537762 78 	kLZ4Compression supported: 0
2025/08/28-06:43:08.537766 78 	kZlibCompression supported: 0
2025/08/28-06:43:08.537771 78 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.537775 78 	kSnappyCompression supported: 1
2025/08/28-06:43:08.537785 78 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.537789 78 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.556261 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.556270 78           Options.merge_operator: None
2025/08/28-06:43:08.556274 78        Options.compaction_filter: None
2025/08/28-06:43:08.556283 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.556287 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.556291 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.556294 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.556397 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f650000d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f650000d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.556407 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.556411 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.556416 78          Options.compression: Snappy
2025/08/28-06:43:08.556425 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.556429 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.556435 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.556439 78             Options.num_levels: 7
2025/08/28-06:43:08.556442 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.556446 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.556452 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.556456 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.556460 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.556464 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.556467 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556470 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556473 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556477 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.556480 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556483 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556487 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.556496 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.556501 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.556504 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.556507 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.556511 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.556518 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.556525 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.556528 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.556535 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.556538 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.556548 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.556552 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.556558 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.556562 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.556566 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.556571 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.556576 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.556580 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.556583 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.556586 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.556590 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.556597 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.556603 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.556611 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.556615 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.556656 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.556660 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.556663 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.556736 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.556740 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.556745 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.556757 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.556766 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.556776 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.556780 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.556784 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.556787 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.556792 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.556795 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.556799 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.556810 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.556824 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.556827 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.556832 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.556836 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.556840 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.556846 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.556849 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.556852 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.556856 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.556859 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.556862 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.556866 78                               Options.ttl: 2592000
2025/08/28-06:43:08.556869 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.556875 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.556878 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.556881 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.556885 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.556895 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.556899 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.556911 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.556931 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.557457 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.557464 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.557468 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.557471 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.585000 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.585008 78           Options.merge_operator: None
2025/08/28-06:43:08.585012 78        Options.compaction_filter: None
2025/08/28-06:43:08.585016 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.585019 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.585022 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.585025 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.585098 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f6500003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f6500003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.585104 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.585108 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.585112 78          Options.compression: Snappy
2025/08/28-06:43:08.585115 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.585118 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.585121 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.585124 78             Options.num_levels: 7
2025/08/28-06:43:08.585127 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.585130 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.585133 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.585136 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.585139 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.585142 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.585145 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.585148 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.585151 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.585153 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.585156 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.585159 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.585162 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.585165 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.585167 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.585170 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.585173 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.585176 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.585184 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.585187 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.585190 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.585193 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.585197 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.585200 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.585203 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.585206 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.585209 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.585212 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.585217 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.585222 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.585230 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.585234 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.585238 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.585241 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.585244 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.585246 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.585249 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.585252 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.585255 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.585259 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.585262 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.585265 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.585268 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.585272 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.585276 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.585279 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.585282 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.585286 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.585294 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.585298 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.585302 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.585305 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.585308 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.585320 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.585323 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.585327 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.585331 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.585335 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.585338 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.585341 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.585344 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.585352 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.585356 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.585359 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.585362 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.585365 78                               Options.ttl: 2592000
2025/08/28-06:43:08.585368 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.585371 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.585373 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.585377 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.585380 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.585382 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.585386 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.585389 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.585393 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.585397 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.585400 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.585409 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.585413 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.598833 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.598844 78           Options.merge_operator: None
2025/08/28-06:43:08.598849 78        Options.compaction_filter: None
2025/08/28-06:43:08.598852 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.598856 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.598859 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.598866 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.598933 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f6500005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f65000061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.598939 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.598943 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.598947 78          Options.compression: Snappy
2025/08/28-06:43:08.598950 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.598954 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.598957 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.598959 78             Options.num_levels: 7
2025/08/28-06:43:08.598963 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.598966 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.598969 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.598972 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.598975 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.598978 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.598985 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.598988 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.598991 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.598994 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.599000 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599003 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599006 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599010 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599014 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599017 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599020 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599023 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599026 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599030 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599034 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599037 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599042 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599045 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599048 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599053 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599056 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599060 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599065 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599069 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599072 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599075 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599078 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599118 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599124 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599128 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599131 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599139 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599142 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599146 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599150 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599153 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599156 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599161 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599165 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599168 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599171 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599174 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599177 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599180 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599186 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599189 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599192 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599202 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599205 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599208 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599214 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599217 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599220 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599223 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.599226 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599229 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599232 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599234 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599237 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599239 78                               Options.ttl: 2592000
2025/08/28-06:43:08.599245 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599248 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599251 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599254 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.599257 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.599259 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599262 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599265 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599270 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599274 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599279 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599282 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599287 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.619065 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.619076 78           Options.merge_operator: None
2025/08/28-06:43:08.619079 78        Options.compaction_filter: None
2025/08/28-06:43:08.619084 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.619087 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.619091 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.619096 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.619158 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f6500008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f6500008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.619167 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.619171 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.619176 78          Options.compression: Snappy
2025/08/28-06:43:08.619180 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.619183 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.619187 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.619191 78             Options.num_levels: 7
2025/08/28-06:43:08.619195 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.619200 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.619204 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.619208 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.619211 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.619215 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.619218 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619222 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619226 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619230 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.619234 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619238 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619242 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.619246 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.619250 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.619254 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619258 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619262 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619266 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619269 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.619273 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619277 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.619281 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.619285 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.619289 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.619293 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.619296 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.619300 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.619307 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.619312 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.619316 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.619320 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.619323 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.619327 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.619331 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.619335 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.619339 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.619342 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.619346 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.619350 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.619354 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.619358 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.619361 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.619366 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.619371 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.619375 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.619378 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.619382 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.619385 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.619389 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.619393 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.619397 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.619401 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.619411 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.619415 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.619419 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.619424 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.619428 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.619431 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.619435 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.619439 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.619443 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.619446 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.619449 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.619452 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.619456 78                               Options.ttl: 2592000
2025/08/28-06:43:08.619459 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.619463 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.619466 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.619469 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.619473 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.619476 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.619480 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.619484 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.619489 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.619493 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.619497 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.619501 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.619506 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637747 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637758 78           Options.merge_operator: None
2025/08/28-06:43:08.637761 78        Options.compaction_filter: None
2025/08/28-06:43:08.637763 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637766 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637769 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637771 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637815 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f650000ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f650000aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637820 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637822 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637825 78          Options.compression: Snappy
2025/08/28-06:43:08.637828 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637831 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637833 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637836 78             Options.num_levels: 7
2025/08/28-06:43:08.637839 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637841 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637844 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637847 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.637850 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.637853 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.637856 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637859 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637862 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637864 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.637867 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637870 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637873 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.637875 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.637878 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.637881 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637883 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637887 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637890 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637892 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.637895 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637898 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.637900 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.637903 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.637907 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.637909 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.637912 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.637915 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.637919 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.637923 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.637926 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.637928 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.637932 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.637934 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.637937 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.637941 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.637944 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.637947 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.637950 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.637952 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.637955 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.637957 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.637960 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.637963 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.637966 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.637969 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.637972 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.637974 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.637977 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.637984 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.637986 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.637989 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.637991 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.637999 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638007 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638009 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638011 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638012 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638013 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638015 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.638016 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638017 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638019 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638020 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638021 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638023 78                               Options.ttl: 2592000
2025/08/28-06:43:08.638024 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638025 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638026 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638028 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.638029 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.638030 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638031 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638032 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638033 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638035 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638036 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638038 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638039 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.657393 78 DB pointer 0x7f6500014700
