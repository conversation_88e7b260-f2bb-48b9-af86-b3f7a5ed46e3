2025/08/28-06:43:08.550265 73 RocksDB version: 8.1.1
2025/08/28-06:43:08.550444 73 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550451 73 DB SUMMARY
2025/08/28-06:43:08.550456 73 DB Session ID:  A875X3AMNRT93RQW6NDI
2025/08/28-06:43:08.550501 73 SST files in ./storage/collections/example_collection/1/segments/fe319111-db70-4b18-8c8e-d5e231d4e3d2 dir, Total Num: 0, files: 
2025/08/28-06:43:08.550511 73 Write Ahead Log file in ./storage/collections/example_collection/1/segments/fe319111-db70-4b18-8c8e-d5e231d4e3d2: 
2025/08/28-06:43:08.550518 73                         Options.error_if_exists: 0
2025/08/28-06:43:08.550525 73                       Options.create_if_missing: 1
2025/08/28-06:43:08.550531 73                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550536 73             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550543 73                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550548 73        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550554 73                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550563 73                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550568 73                                Options.info_log: 0x7f3c38011e00
2025/08/28-06:43:08.550574 73                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550579 73                              Options.statistics: (nil)
2025/08/28-06:43:08.550584 73                               Options.use_fsync: 0
2025/08/28-06:43:08.550590 73                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550595 73                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550600 73                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550608 73                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550615 73                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550620 73                         Options.allow_fallocate: 1
2025/08/28-06:43:08.550625 73                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.550663 73                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.550668 73                        Options.use_direct_reads: 0
2025/08/28-06:43:08.550672 73                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.550677 73          Options.create_missing_column_families: 1
2025/08/28-06:43:08.550713 73                              Options.db_log_dir: 
2025/08/28-06:43:08.550718 73                                 Options.wal_dir: 
2025/08/28-06:43:08.550722 73                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.550727 73                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.550737 73                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.550747 73                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.550753 73             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.550758 73                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.550762 73                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.550774 73                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.550780 73                    Options.write_buffer_manager: 0x7f3c380113a0
2025/08/28-06:43:08.550794 73         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.550799 73           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.550804 73                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.550808 73                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.550818 73     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.550830 73                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.550842 73                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.550847 73                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.550863 73                  Options.unordered_write: 0
2025/08/28-06:43:08.550897 73         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.550908 73      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.550912 73             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.550938 73            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.550978 73                               Options.row_cache: None
2025/08/28-06:43:08.550991 73                              Options.wal_filter: None
2025/08/28-06:43:08.550996 73             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.550999 73             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.551003 73             Options.two_write_queues: 0
2025/08/28-06:43:08.551007 73             Options.manual_wal_flush: 0
2025/08/28-06:43:08.551010 73             Options.wal_compression: 0
2025/08/28-06:43:08.551017 73             Options.atomic_flush: 0
2025/08/28-06:43:08.551023 73             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.551028 73                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.551032 73                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.551036 73                 Options.log_readahead_size: 0
2025/08/28-06:43:08.551040 73                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.551044 73                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.551048 73                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.551053 73            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.551057 73             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.551060 73             Options.db_host_id: __hostname__
2025/08/28-06:43:08.551064 73             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.551069 73             Options.max_background_jobs: 2
2025/08/28-06:43:08.551073 73             Options.max_background_compactions: -1
2025/08/28-06:43:08.551077 73             Options.max_subcompactions: 1
2025/08/28-06:43:08.551082 73             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.551086 73           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.551094 73             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.551098 73             Options.max_total_wal_size: 0
2025/08/28-06:43:08.551103 73             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.551107 73                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.551112 73                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.551116 73                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.551120 73                          Options.max_open_files: 256
2025/08/28-06:43:08.551125 73                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.551128 73                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.551131 73                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.551133 73       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.551136 73                  Options.max_background_flushes: -1
2025/08/28-06:43:08.551154 73 Compression algorithms supported:
2025/08/28-06:43:08.551161 73 	kZSTD supported: 0
2025/08/28-06:43:08.551166 73 	kXpressCompression supported: 0
2025/08/28-06:43:08.551173 73 	kBZip2Compression supported: 0
2025/08/28-06:43:08.551178 73 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.551182 73 	kLZ4Compression supported: 0
2025/08/28-06:43:08.551187 73 	kZlibCompression supported: 0
2025/08/28-06:43:08.551191 73 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.551195 73 	kSnappyCompression supported: 1
2025/08/28-06:43:08.551200 73 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.551203 73 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571421 73               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.571433 73           Options.merge_operator: None
2025/08/28-06:43:08.571438 73        Options.compaction_filter: None
2025/08/28-06:43:08.571445 73        Options.compaction_filter_factory: None
2025/08/28-06:43:08.571447 73  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.571450 73         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.571458 73            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.571542 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c3800eac0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c3800edf0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.571556 73        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.571559 73  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.571565 73          Options.compression: Snappy
2025/08/28-06:43:08.571568 73                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.571589 73       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.571592 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.571594 73             Options.num_levels: 7
2025/08/28-06:43:08.571597 73        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.571605 73     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.571625 73     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.571634 73            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.571637 73                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.571639 73               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.571642 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571654 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571662 73         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571670 73                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.571674 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571677 73         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571683 73            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.571687 73                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.571690 73               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.571693 73         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571695 73         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571698 73         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571776 73         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571779 73                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.571782 73         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571784 73      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.571880 73          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.571885 73              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.571888 73                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.571890 73             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.571893 73                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.571916 73 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.571920 73          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.571931 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.571934 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.571936 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.571938 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.571940 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.571942 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.571944 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.571947 73       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.571963 73                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.571978 73   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.571981 73                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.571987 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.572002 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.572019 73                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.572022 73                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.572026 73                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.572028 73 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.572036 73 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.572038 73 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.572040 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.572043 73 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.572056 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.572062 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.572064 73 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.572072 73                   Options.table_properties_collectors: 
2025/08/28-06:43:08.572074 73                   Options.inplace_update_support: 0
2025/08/28-06:43:08.572076 73                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.572079 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.572082 73               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.572103 73   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.572106 73                           Options.bloom_locality: 0
2025/08/28-06:43:08.572108 73                    Options.max_successive_merges: 0
2025/08/28-06:43:08.572110 73                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.572112 73                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.572114 73                Options.force_consistency_checks: 1
2025/08/28-06:43:08.572116 73                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.572119 73                               Options.ttl: 2592000
2025/08/28-06:43:08.572121 73          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.572126 73  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.572128 73    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.572134 73                       Options.enable_blob_files: false
2025/08/28-06:43:08.572136 73                           Options.min_blob_size: 0
2025/08/28-06:43:08.572141 73                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.572144 73                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.572146 73          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.572149 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.572154 73 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.572157 73          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.572160 73                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.572162 73 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.598942 73               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.598951 73           Options.merge_operator: None
2025/08/28-06:43:08.598955 73        Options.compaction_filter: None
2025/08/28-06:43:08.598958 73        Options.compaction_filter_factory: None
2025/08/28-06:43:08.598960 73  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.598963 73         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.598965 73            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.599036 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c38005150)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c38005480
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.599042 73        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.599045 73  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.599048 73          Options.compression: Snappy
2025/08/28-06:43:08.599051 73                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.599053 73       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.599056 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.599058 73             Options.num_levels: 7
2025/08/28-06:43:08.599061 73        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.599063 73     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.599066 73     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.599069 73            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.599071 73                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.599074 73               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.599077 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599080 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599083 73         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599085 73                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.599088 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599090 73         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599093 73            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.599096 73                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.599098 73               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.599101 73         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.599103 73         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.599106 73         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.599108 73         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.599111 73                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.599113 73         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.599116 73      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.599119 73          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.599121 73              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.599124 73                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.599127 73             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.599129 73                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.599132 73 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.599137 73          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.599141 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.599144 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.599147 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.599149 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.599156 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.599160 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.599162 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.599165 73       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.599167 73                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.599170 73   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.599173 73                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.599175 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.599178 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.599181 73                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.599185 73                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.599188 73                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.599190 73 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.599193 73 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.599195 73 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.599198 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.599201 73 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.599204 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.599207 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.599209 73 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.599219 73                   Options.table_properties_collectors: 
2025/08/28-06:43:08.599222 73                   Options.inplace_update_support: 0
2025/08/28-06:43:08.599225 73                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.599229 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.599232 73               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.599235 73   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.599237 73                           Options.bloom_locality: 0
2025/08/28-06:43:08.599240 73                    Options.max_successive_merges: 0
2025/08/28-06:43:08.599247 73                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.599251 73                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.599253 73                Options.force_consistency_checks: 1
2025/08/28-06:43:08.599255 73                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.599258 73                               Options.ttl: 2592000
2025/08/28-06:43:08.599261 73          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.599263 73  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.599266 73    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.599268 73                       Options.enable_blob_files: false
2025/08/28-06:43:08.599271 73                           Options.min_blob_size: 0
2025/08/28-06:43:08.599273 73                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.599276 73                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.599279 73          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.599282 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.599286 73 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.599289 73          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.599292 73                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.599295 73 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.615491 73               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.615494 73           Options.merge_operator: None
2025/08/28-06:43:08.615496 73        Options.compaction_filter: None
2025/08/28-06:43:08.615497 73        Options.compaction_filter_factory: None
2025/08/28-06:43:08.615499 73  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.615500 73         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.615501 73            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.615519 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c38007760)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c38007a90
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.615520 73        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.615522 73  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.615523 73          Options.compression: Snappy
2025/08/28-06:43:08.615524 73                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.615525 73       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.615526 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.615527 73             Options.num_levels: 7
2025/08/28-06:43:08.615528 73        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.615530 73     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.615531 73     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.615532 73            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.615533 73                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.615534 73               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.615536 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615537 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615539 73         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615540 73                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.615541 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615542 73         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615543 73            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.615545 73                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.615546 73               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.615547 73         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.615548 73         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.615550 73         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.615551 73         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.615552 73                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.615553 73         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.615554 73      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.615555 73          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.615556 73              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.615557 73                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.615558 73             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.615559 73                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.615560 73 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.615562 73          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.615563 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.615565 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.615566 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.615567 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.615568 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.615569 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.615570 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.615571 73       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.615572 73                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.615574 73   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.615575 73                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.615576 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.615577 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.615578 73                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.615580 73                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.615582 73                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.615583 73 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.615584 73 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.615585 73 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.615586 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.615588 73 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.615589 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.615590 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.615591 73 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.615596 73                   Options.table_properties_collectors: 
2025/08/28-06:43:08.615597 73                   Options.inplace_update_support: 0
2025/08/28-06:43:08.615598 73                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.615600 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.615602 73               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.615603 73   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.615604 73                           Options.bloom_locality: 0
2025/08/28-06:43:08.615605 73                    Options.max_successive_merges: 0
2025/08/28-06:43:08.615606 73                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.615607 73                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.615608 73                Options.force_consistency_checks: 1
2025/08/28-06:43:08.615610 73                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.615611 73                               Options.ttl: 2592000
2025/08/28-06:43:08.615612 73          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.615613 73  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.615614 73    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.615616 73                       Options.enable_blob_files: false
2025/08/28-06:43:08.615617 73                           Options.min_blob_size: 0
2025/08/28-06:43:08.615618 73                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.615619 73                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.615621 73          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.615622 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.615624 73 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.615625 73          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.615626 73                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.615627 73 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.627936 73               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.627945 73           Options.merge_operator: None
2025/08/28-06:43:08.627948 73        Options.compaction_filter: None
2025/08/28-06:43:08.627951 73        Options.compaction_filter_factory: None
2025/08/28-06:43:08.627954 73  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.627957 73         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.627960 73            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.628018 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c38009dc0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c3800a0f0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.628025 73        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.628028 73  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.628031 73          Options.compression: Snappy
2025/08/28-06:43:08.628034 73                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.628037 73       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.628040 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.628043 73             Options.num_levels: 7
2025/08/28-06:43:08.628046 73        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.628049 73     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.628052 73     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.628056 73            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.628059 73                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.628062 73               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.628065 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.628068 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.628071 73         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.628074 73                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.628077 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.628080 73         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.628083 73            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.628086 73                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.628089 73               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.628092 73         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.628095 73         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.628097 73         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.628100 73         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.628103 73                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.628106 73         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.628109 73      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.628112 73          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.628115 73              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.628118 73                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.628121 73             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.628124 73                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.628127 73 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.628132 73          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.628137 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.628140 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.628144 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.628146 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.628149 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.628152 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.628155 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.628157 73       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.628161 73                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.628163 73   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.628166 73                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.628169 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.628172 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.628175 73                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.628179 73                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.628183 73                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.628186 73 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.628188 73 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.628191 73 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.628194 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.628197 73 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.628201 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.628204 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.628207 73 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.628217 73                   Options.table_properties_collectors: 
2025/08/28-06:43:08.628221 73                   Options.inplace_update_support: 0
2025/08/28-06:43:08.628224 73                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.628228 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.628232 73               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.628235 73   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.628237 73                           Options.bloom_locality: 0
2025/08/28-06:43:08.628240 73                    Options.max_successive_merges: 0
2025/08/28-06:43:08.628243 73                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.628245 73                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.628248 73                Options.force_consistency_checks: 1
2025/08/28-06:43:08.628251 73                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.628254 73                               Options.ttl: 2592000
2025/08/28-06:43:08.628257 73          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.628259 73  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.628262 73    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.628265 73                       Options.enable_blob_files: false
2025/08/28-06:43:08.628267 73                           Options.min_blob_size: 0
2025/08/28-06:43:08.628270 73                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.628273 73                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.628276 73          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.628280 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.628284 73 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.628288 73          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.628291 73                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.628295 73 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.642898 73               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.642905 73           Options.merge_operator: None
2025/08/28-06:43:08.642907 73        Options.compaction_filter: None
2025/08/28-06:43:08.642910 73        Options.compaction_filter_factory: None
2025/08/28-06:43:08.642912 73  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.642916 73         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.642919 73            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.642960 73            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c3800c440)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c3800c770
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.642964 73        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.642966 73  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.642969 73          Options.compression: Snappy
2025/08/28-06:43:08.642971 73                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.642974 73       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.642975 73   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.642978 73             Options.num_levels: 7
2025/08/28-06:43:08.642980 73        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.642983 73     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.642985 73     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.642987 73            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.642989 73                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.642991 73               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.642993 73         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.642996 73         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.642998 73         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.643000 73                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.643002 73         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.643004 73         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.643007 73            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.643009 73                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.643011 73               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.643013 73         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.643015 73         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.643017 73         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.643019 73         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.643023 73                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.643025 73         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.643027 73      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.643029 73          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.643032 73              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.643034 73                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.643036 73             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.643038 73                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.643042 73 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.643046 73          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.643049 73 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.643051 73 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.643053 73 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.643056 73 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.643058 73 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.643060 73 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.643062 73 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.643064 73       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.643066 73                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.643069 73   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.643071 73                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.643073 73   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.643075 73   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.643077 73                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.643080 73                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.643083 73                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.643085 73 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.643087 73 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.643090 73 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.643092 73 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.643094 73 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.643096 73 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.643099 73 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.643101 73 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.643109 73                   Options.table_properties_collectors: 
2025/08/28-06:43:08.643111 73                   Options.inplace_update_support: 0
2025/08/28-06:43:08.643114 73                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.643116 73               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.643119 73               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.643121 73   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.643123 73                           Options.bloom_locality: 0
2025/08/28-06:43:08.643125 73                    Options.max_successive_merges: 0
2025/08/28-06:43:08.643127 73                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.643129 73                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.643131 73                Options.force_consistency_checks: 1
2025/08/28-06:43:08.643133 73                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.643135 73                               Options.ttl: 2592000
2025/08/28-06:43:08.643138 73          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.643140 73  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.643142 73    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.643144 73                       Options.enable_blob_files: false
2025/08/28-06:43:08.643146 73                           Options.min_blob_size: 0
2025/08/28-06:43:08.643148 73                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.643150 73                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.643153 73          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.643156 73      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.643159 73 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.643162 73          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.643165 73                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.643167 73 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.660765 73 DB pointer 0x7f3c38015fc0
