2025/08/28-06:43:08.550265 76 RocksDB version: 8.1.1
2025/08/28-06:43:08.550459 76 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550467 76 DB SUMMARY
2025/08/28-06:43:08.550471 76 DB Session ID:  A875X3AMNRT93RQW6NDJ
2025/08/28-06:43:08.550534 76 SST files in ./storage/collections/example_collection/1/segments/467f3407-a489-42c2-9929-c2153f0c8cb4 dir, Total Num: 0, files: 
2025/08/28-06:43:08.550541 76 Write Ahead Log file in ./storage/collections/example_collection/1/segments/467f3407-a489-42c2-9929-c2153f0c8cb4: 
2025/08/28-06:43:08.550546 76                         Options.error_if_exists: 0
2025/08/28-06:43:08.550550 76                       Options.create_if_missing: 1
2025/08/28-06:43:08.550554 76                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550561 76             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550572 76                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550577 76        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550581 76                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550585 76                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550589 76                                Options.info_log: 0x7f3c28010540
2025/08/28-06:43:08.550593 76                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550597 76                              Options.statistics: (nil)
2025/08/28-06:43:08.550601 76                               Options.use_fsync: 0
2025/08/28-06:43:08.550604 76                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550608 76                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550617 76                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550621 76                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550625 76                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550642 76                         Options.allow_fallocate: 1
2025/08/28-06:43:08.550646 76                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.550649 76                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.550657 76                        Options.use_direct_reads: 0
2025/08/28-06:43:08.550661 76                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.550664 76          Options.create_missing_column_families: 1
2025/08/28-06:43:08.550668 76                              Options.db_log_dir: 
2025/08/28-06:43:08.550671 76                                 Options.wal_dir: 
2025/08/28-06:43:08.550677 76                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.550688 76                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.550694 76                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.550698 76                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.550719 76             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.550723 76                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.550727 76                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.550734 76                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.550747 76                    Options.write_buffer_manager: 0x7f3c2800fae0
2025/08/28-06:43:08.550758 76         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.550766 76           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.550770 76                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.550774 76                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.550781 76     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.550816 76                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.550820 76                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.550831 76                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.550860 76                  Options.unordered_write: 0
2025/08/28-06:43:08.550870 76         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.550874 76      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.550880 76             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.550884 76            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.550887 76                               Options.row_cache: None
2025/08/28-06:43:08.550891 76                              Options.wal_filter: None
2025/08/28-06:43:08.550935 76             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.550938 76             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.550971 76             Options.two_write_queues: 0
2025/08/28-06:43:08.550974 76             Options.manual_wal_flush: 0
2025/08/28-06:43:08.550978 76             Options.wal_compression: 0
2025/08/28-06:43:08.551007 76             Options.atomic_flush: 0
2025/08/28-06:43:08.551010 76             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.551017 76                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.551023 76                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.551026 76                 Options.log_readahead_size: 0
2025/08/28-06:43:08.551030 76                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.551034 76                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.551037 76                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.551041 76            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.551045 76             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.551048 76             Options.db_host_id: __hostname__
2025/08/28-06:43:08.551059 76             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.551064 76             Options.max_background_jobs: 2
2025/08/28-06:43:08.551068 76             Options.max_background_compactions: -1
2025/08/28-06:43:08.551072 76             Options.max_subcompactions: 1
2025/08/28-06:43:08.551075 76             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.551079 76           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.551083 76             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.551086 76             Options.max_total_wal_size: 0
2025/08/28-06:43:08.551090 76             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.551094 76                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.551100 76                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.551104 76                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.551107 76                          Options.max_open_files: 256
2025/08/28-06:43:08.551114 76                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.551117 76                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.551121 76                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.551126 76       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.551130 76                  Options.max_background_flushes: -1
2025/08/28-06:43:08.551134 76 Compression algorithms supported:
2025/08/28-06:43:08.551139 76 	kZSTD supported: 0
2025/08/28-06:43:08.551143 76 	kXpressCompression supported: 0
2025/08/28-06:43:08.551147 76 	kBZip2Compression supported: 0
2025/08/28-06:43:08.551151 76 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.551155 76 	kLZ4Compression supported: 0
2025/08/28-06:43:08.551158 76 	kZlibCompression supported: 0
2025/08/28-06:43:08.551162 76 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.551166 76 	kSnappyCompression supported: 1
2025/08/28-06:43:08.551173 76 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.551177 76 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571366 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.571380 76           Options.merge_operator: None
2025/08/28-06:43:08.571385 76        Options.compaction_filter: None
2025/08/28-06:43:08.571393 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.571538 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.571542 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.571550 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.571608 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2800d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2800d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.571619 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.571622 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.571628 76          Options.compression: Snappy
2025/08/28-06:43:08.571632 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.571638 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.571642 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.571654 76             Options.num_levels: 7
2025/08/28-06:43:08.571659 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.571663 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.571667 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.571671 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.571675 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.571679 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.571685 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571689 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571693 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571699 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.571713 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571718 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571721 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.571725 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.571732 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.571739 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571744 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571750 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571754 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571780 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.571787 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571790 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.571794 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.571801 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.571805 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.571808 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.571815 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.571819 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.571826 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.571831 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.571835 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.571853 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.571858 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.571861 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.571865 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.571870 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.571874 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.571892 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.571905 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.571908 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.571926 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.571933 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.571936 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.571943 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.571952 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.571959 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.571964 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.571970 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.571974 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.571980 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.571986 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.571994 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.572001 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.572011 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.572018 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.572022 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.572027 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.572052 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.572055 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.572061 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.572064 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.572069 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.572074 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.572077 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.572081 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.572084 76                               Options.ttl: 2592000
2025/08/28-06:43:08.572087 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.572091 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.572094 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.572098 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.572102 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.572109 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.572114 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.572122 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.572128 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.572146 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.572150 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.572153 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.572157 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.587566 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.587578 76           Options.merge_operator: None
2025/08/28-06:43:08.587582 76        Options.compaction_filter: None
2025/08/28-06:43:08.587586 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.587589 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.587593 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.587603 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.588363 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c28003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c28003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.588376 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.588379 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.588385 76          Options.compression: Snappy
2025/08/28-06:43:08.588389 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.588393 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.588396 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.588399 76             Options.num_levels: 7
2025/08/28-06:43:08.588402 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.588404 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.588407 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.588410 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.588415 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.588419 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.588422 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.588426 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.588429 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.588435 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.588439 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.588442 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.588446 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.588450 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.588454 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.588458 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.588461 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.588465 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.588468 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.588471 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.588475 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.588478 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.588481 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.588485 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.588489 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.588492 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.588496 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.588500 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.588508 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.588513 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.588518 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.588522 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.588525 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.588529 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.588532 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.588535 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.588539 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.588543 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.588546 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.588550 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.588557 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.588561 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.588565 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.588570 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.588574 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.588577 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.588581 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.588585 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.588589 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.588593 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.588597 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.588601 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.588605 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.588616 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.588619 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.588623 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.588628 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.588632 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.588635 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.588639 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.588642 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.588646 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.588650 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.588653 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.588656 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.588660 76                               Options.ttl: 2592000
2025/08/28-06:43:08.588663 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.588667 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.588670 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.588677 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.588681 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.588684 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.588689 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.588692 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.588697 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.588715 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.588719 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.588723 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.588727 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601793 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601801 76           Options.merge_operator: None
2025/08/28-06:43:08.601805 76        Options.compaction_filter: None
2025/08/28-06:43:08.601808 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601811 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601815 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601819 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.601882 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c28005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c280061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.601887 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.601891 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.601894 76          Options.compression: Snappy
2025/08/28-06:43:08.601898 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.601901 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.601904 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.601907 76             Options.num_levels: 7
2025/08/28-06:43:08.601911 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.601914 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.601917 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.601920 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.601924 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.601927 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.601930 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601934 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601937 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601940 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.601949 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601952 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601955 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.601959 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.601962 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.601965 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601970 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601974 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601979 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601982 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.601985 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601989 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.601992 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.601995 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.601998 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.602001 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.602004 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602007 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602012 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602017 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602021 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602024 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602027 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602030 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602033 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602036 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602040 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602043 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602046 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602049 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602053 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602056 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602059 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602064 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602067 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602072 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602076 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602081 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602084 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602088 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602091 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602095 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602098 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602108 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602111 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602114 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602118 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602122 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602126 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602129 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.602132 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602135 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602138 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602141 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602145 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602147 76                               Options.ttl: 2592000
2025/08/28-06:43:08.602150 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602156 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602159 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602162 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.602165 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.602168 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602172 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602175 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602179 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602183 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602186 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602189 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602193 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.617550 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.617558 76           Options.merge_operator: None
2025/08/28-06:43:08.617562 76        Options.compaction_filter: None
2025/08/28-06:43:08.617566 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.617569 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.617573 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.617577 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.617629 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c28008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c28008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.617635 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.617639 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.617643 76          Options.compression: Snappy
2025/08/28-06:43:08.617648 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.617652 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.617655 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.617658 76             Options.num_levels: 7
2025/08/28-06:43:08.617662 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.617665 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.617669 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.617673 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.617684 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.617685 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.617686 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617688 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617689 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617690 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.617691 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617692 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617693 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.617694 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.617695 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.617697 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617698 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617699 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617730 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617732 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.617733 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617735 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.617736 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.617737 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.617739 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.617740 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.617742 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.617743 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.617746 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.617747 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.617749 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.617750 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.617751 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.617753 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.617754 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.617755 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.617756 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.617758 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.617759 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.617760 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.617762 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.617763 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.617764 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.617766 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.617767 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.617769 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.617770 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.617771 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.617773 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.617774 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.617775 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.617777 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.617778 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.617781 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.617782 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.617784 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.617785 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.617786 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.617788 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.617789 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.617790 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.617791 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.617793 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.617794 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.617795 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.617796 76                               Options.ttl: 2592000
2025/08/28-06:43:08.617797 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.617799 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.617800 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.617801 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.617802 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.617803 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.617805 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.617806 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.617808 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.617810 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.617811 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.617812 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.617814 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.638259 76               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.638261 76           Options.merge_operator: None
2025/08/28-06:43:08.638262 76        Options.compaction_filter: None
2025/08/28-06:43:08.638263 76        Options.compaction_filter_factory: None
2025/08/28-06:43:08.638265 76  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.638266 76         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.638267 76            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.638287 76            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2800ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2800aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.638289 76        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.638290 76  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.638292 76          Options.compression: Snappy
2025/08/28-06:43:08.638293 76                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.638294 76       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.638295 76   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.638296 76             Options.num_levels: 7
2025/08/28-06:43:08.638298 76        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.638299 76     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.638300 76     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.638301 76            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.638302 76                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.638304 76               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.638305 76         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638306 76         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638308 76         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638309 76                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.638310 76         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638312 76         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638313 76            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.638314 76                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.638316 76               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.638317 76         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638318 76         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638319 76         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638321 76         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638322 76                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.638323 76         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638324 76      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.638326 76          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.638327 76              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.638328 76                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.638330 76             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.638332 76                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.638333 76 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.638335 76          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.638337 76 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.638338 76 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.638339 76 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.638341 76 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.638342 76 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.638343 76 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.638344 76 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.638345 76       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.638347 76                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.638348 76   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.638349 76                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.638350 76   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.638351 76   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.638352 76                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.638354 76                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.638355 76                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.638356 76 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.638357 76 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.638359 76 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.638360 76 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.638361 76 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.638362 76 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638363 76 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638365 76 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638368 76                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638369 76                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638370 76                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638371 76               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638373 76               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638374 76   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638375 76                           Options.bloom_locality: 0
2025/08/28-06:43:08.638377 76                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638378 76                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638379 76                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638380 76                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638382 76                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638383 76                               Options.ttl: 2592000
2025/08/28-06:43:08.638384 76          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638385 76  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638386 76    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638387 76                       Options.enable_blob_files: false
2025/08/28-06:43:08.638388 76                           Options.min_blob_size: 0
2025/08/28-06:43:08.638389 76                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638390 76                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638391 76          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638392 76      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638394 76 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638395 76          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638396 76                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638397 76 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.660744 76 DB pointer 0x7f3c28014700
