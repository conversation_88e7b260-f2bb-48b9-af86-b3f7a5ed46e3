2025/08/28-06:43:08.663613 80 RocksDB version: 8.1.1
2025/08/28-06:43:08.663622 80 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.663623 80 DB SUMMARY
2025/08/28-06:43:08.663625 80 DB Session ID:  A875X3AMNRT93RQW6NDN
2025/08/28-06:43:08.663633 80 SST files in ./storage/collections/example_collection/1/segments/1feaae5f-06c6-4a00-828a-0f60cc66da9f/payload_index dir, Total Num: 0, files: 
2025/08/28-06:43:08.663635 80 Write Ahead Log file in ./storage/collections/example_collection/1/segments/1feaae5f-06c6-4a00-828a-0f60cc66da9f/payload_index: 
2025/08/28-06:43:08.663636 80                         Options.error_if_exists: 0
2025/08/28-06:43:08.663637 80                       Options.create_if_missing: 1
2025/08/28-06:43:08.663638 80                         Options.paranoid_checks: 1
2025/08/28-06:43:08.663639 80             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.663640 80                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.663640 80        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.663641 80                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.663642 80                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.663643 80                                Options.info_log: 0x7f3c1808a730
2025/08/28-06:43:08.663645 80                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.663646 80                              Options.statistics: (nil)
2025/08/28-06:43:08.663648 80                               Options.use_fsync: 0
2025/08/28-06:43:08.663649 80                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.663651 80                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.663651 80                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.663652 80                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.663653 80                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.663654 80                         Options.allow_fallocate: 1
2025/08/28-06:43:08.663655 80                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.663656 80                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.663657 80                        Options.use_direct_reads: 0
2025/08/28-06:43:08.663657 80                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.663659 80          Options.create_missing_column_families: 1
2025/08/28-06:43:08.663660 80                              Options.db_log_dir: 
2025/08/28-06:43:08.663661 80                                 Options.wal_dir: 
2025/08/28-06:43:08.663663 80                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.663664 80                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.663666 80                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.663667 80                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.663669 80             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.663670 80                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.663671 80                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.663673 80                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.663674 80                    Options.write_buffer_manager: 0x7f3c1809ee70
2025/08/28-06:43:08.663676 80         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.663677 80           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.663679 80                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.663680 80                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.663682 80     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.663683 80                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.663685 80                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.663686 80                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.663688 80                  Options.unordered_write: 0
2025/08/28-06:43:08.663689 80         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.663691 80      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.663693 80             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.663694 80            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.663695 80                               Options.row_cache: None
2025/08/28-06:43:08.663697 80                              Options.wal_filter: None
2025/08/28-06:43:08.663698 80             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.663705 80             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.663707 80             Options.two_write_queues: 0
2025/08/28-06:43:08.663708 80             Options.manual_wal_flush: 0
2025/08/28-06:43:08.663709 80             Options.wal_compression: 0
2025/08/28-06:43:08.663711 80             Options.atomic_flush: 0
2025/08/28-06:43:08.663712 80             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.663714 80                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.663715 80                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.663716 80                 Options.log_readahead_size: 0
2025/08/28-06:43:08.663717 80                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.663718 80                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.663719 80                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.663720 80            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.663720 80             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.663721 80             Options.db_host_id: __hostname__
2025/08/28-06:43:08.663722 80             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.663723 80             Options.max_background_jobs: 2
2025/08/28-06:43:08.663724 80             Options.max_background_compactions: -1
2025/08/28-06:43:08.663725 80             Options.max_subcompactions: 1
2025/08/28-06:43:08.663726 80             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.663727 80           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.663728 80             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.663728 80             Options.max_total_wal_size: 0
2025/08/28-06:43:08.663729 80             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.663730 80                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.663731 80                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.663732 80                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.663733 80                          Options.max_open_files: 256
2025/08/28-06:43:08.663734 80                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.663734 80                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.663735 80                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.663736 80       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.663737 80                  Options.max_background_flushes: -1
2025/08/28-06:43:08.663738 80 Compression algorithms supported:
2025/08/28-06:43:08.663739 80 	kZSTD supported: 0
2025/08/28-06:43:08.663740 80 	kXpressCompression supported: 0
2025/08/28-06:43:08.663741 80 	kBZip2Compression supported: 0
2025/08/28-06:43:08.663741 80 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.663742 80 	kLZ4Compression supported: 0
2025/08/28-06:43:08.663743 80 	kZlibCompression supported: 0
2025/08/28-06:43:08.663744 80 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.663745 80 	kSnappyCompression supported: 1
2025/08/28-06:43:08.663746 80 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.663747 80 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.672800 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.672805 80           Options.merge_operator: None
2025/08/28-06:43:08.672807 80        Options.compaction_filter: None
2025/08/28-06:43:08.672808 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.672810 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.672812 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.672813 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.672838 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1808f5d0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c180a80a0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.672840 80        Options.write_buffer_size: 10485760
2025/08/28-06:43:08.672842 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.672844 80          Options.compression: Snappy
2025/08/28-06:43:08.672845 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.672847 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.672848 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.672850 80             Options.num_levels: 7
2025/08/28-06:43:08.672851 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.672853 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.672855 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.672856 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.672858 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.672860 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.672861 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.672862 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.672864 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.672865 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.672867 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.672868 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.672869 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.672871 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.672872 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.672874 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.672876 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.672877 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.672878 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.672879 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.672881 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.672882 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.672884 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.672885 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.672887 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.672888 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.672889 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.672891 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.672893 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.672896 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.672897 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.672899 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.672900 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.672901 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.672903 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.672904 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.672905 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.672907 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.672908 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.672910 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.672911 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.672913 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.672914 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.672916 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.672917 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.672919 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.672920 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.672922 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.672923 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.672924 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.672926 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.672928 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.672929 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.672934 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.672935 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.672937 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.672939 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.672940 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.672942 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.672943 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.672945 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.672946 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.672948 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.672949 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.672951 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.672952 80                               Options.ttl: 2592000
2025/08/28-06:43:08.672953 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.672955 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.672956 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.672958 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.672959 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.672961 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.672962 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.672964 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.672966 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.672968 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.672970 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.672971 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.672973 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.685141 80 DB pointer 0x7f3c180aa900
