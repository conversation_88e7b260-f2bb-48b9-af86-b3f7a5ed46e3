2025/08/28-06:43:08.550410 80 RocksDB version: 8.1.1
2025/08/28-06:43:08.550453 80 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550459 80 DB SUMMARY
2025/08/28-06:43:08.550463 80 DB Session ID:  A875X3AMNRT93RQW6NDF
2025/08/28-06:43:08.550501 80 SST files in ./storage/collections/example_collection/1/segments/1feaae5f-06c6-4a00-828a-0f60cc66da9f dir, Total Num: 0, files: 
2025/08/28-06:43:08.550511 80 Write Ahead Log file in ./storage/collections/example_collection/1/segments/1feaae5f-06c6-4a00-828a-0f60cc66da9f: 
2025/08/28-06:43:08.550518 80                         Options.error_if_exists: 0
2025/08/28-06:43:08.550525 80                       Options.create_if_missing: 1
2025/08/28-06:43:08.550532 80                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550536 80             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550548 80                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550552 80        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550562 80                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550569 80                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550578 80                                Options.info_log: 0x7f3c18010540
2025/08/28-06:43:08.550587 80                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550591 80                              Options.statistics: (nil)
2025/08/28-06:43:08.550595 80                               Options.use_fsync: 0
2025/08/28-06:43:08.550600 80                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550606 80                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550611 80                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550619 80                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550623 80                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550627 80                         Options.allow_fallocate: 1
2025/08/28-06:43:08.550635 80                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.550640 80                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.550644 80                        Options.use_direct_reads: 0
2025/08/28-06:43:08.550649 80                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.550653 80          Options.create_missing_column_families: 1
2025/08/28-06:43:08.550658 80                              Options.db_log_dir: 
2025/08/28-06:43:08.550665 80                                 Options.wal_dir: 
2025/08/28-06:43:08.550671 80                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.550697 80                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.550714 80                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.550719 80                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.550724 80             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.550730 80                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.550735 80                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.550745 80                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.550749 80                    Options.write_buffer_manager: 0x7f3c1800fae0
2025/08/28-06:43:08.550779 80         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.550793 80           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.550798 80                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.550803 80                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.550808 80     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.550812 80                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.550817 80                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.550845 80                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.550850 80                  Options.unordered_write: 0
2025/08/28-06:43:08.550858 80         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.550863 80      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.550867 80             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.550872 80            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.550876 80                               Options.row_cache: None
2025/08/28-06:43:08.550896 80                              Options.wal_filter: None
2025/08/28-06:43:08.550901 80             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.550910 80             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.550915 80             Options.two_write_queues: 0
2025/08/28-06:43:08.550919 80             Options.manual_wal_flush: 0
2025/08/28-06:43:08.550937 80             Options.wal_compression: 0
2025/08/28-06:43:08.550942 80             Options.atomic_flush: 0
2025/08/28-06:43:08.550947 80             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.550955 80                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.550971 80                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.550977 80                 Options.log_readahead_size: 0
2025/08/28-06:43:08.550981 80                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.551270 80                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.551277 80                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.551281 80            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.551285 80             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.551290 80             Options.db_host_id: __hostname__
2025/08/28-06:43:08.551301 80             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.551306 80             Options.max_background_jobs: 2
2025/08/28-06:43:08.551310 80             Options.max_background_compactions: -1
2025/08/28-06:43:08.551314 80             Options.max_subcompactions: 1
2025/08/28-06:43:08.551318 80             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.551321 80           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.551325 80             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.551329 80             Options.max_total_wal_size: 0
2025/08/28-06:43:08.551332 80             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.551336 80                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.551340 80                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.551343 80                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.551347 80                          Options.max_open_files: 256
2025/08/28-06:43:08.551350 80                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.551358 80                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.551361 80                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.551367 80       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.551371 80                  Options.max_background_flushes: -1
2025/08/28-06:43:08.551375 80 Compression algorithms supported:
2025/08/28-06:43:08.551378 80 	kZSTD supported: 0
2025/08/28-06:43:08.551398 80 	kXpressCompression supported: 0
2025/08/28-06:43:08.551402 80 	kBZip2Compression supported: 0
2025/08/28-06:43:08.551408 80 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.551412 80 	kLZ4Compression supported: 0
2025/08/28-06:43:08.551416 80 	kZlibCompression supported: 0
2025/08/28-06:43:08.551419 80 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.551423 80 	kSnappyCompression supported: 1
2025/08/28-06:43:08.551428 80 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.551432 80 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571391 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.571410 80           Options.merge_operator: None
2025/08/28-06:43:08.571414 80        Options.compaction_filter: None
2025/08/28-06:43:08.571424 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.571428 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.571431 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.571434 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.571515 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1800d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1800d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.571522 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.571528 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.571535 80          Options.compression: Snappy
2025/08/28-06:43:08.571539 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.571542 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.571555 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.571558 80             Options.num_levels: 7
2025/08/28-06:43:08.571565 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.571569 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.571572 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.571576 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.571579 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.571589 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.571593 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571596 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571608 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571615 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.571618 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571621 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571625 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.571631 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.571640 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.571645 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571650 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571653 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571656 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571659 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.571662 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571667 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.571670 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.571682 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.571690 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.571731 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.571737 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.571740 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.571746 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.571750 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.571754 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.571769 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.571772 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.571777 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.571780 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.571785 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.571788 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.571791 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.571821 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.571828 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.571834 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.571853 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.571858 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.571862 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.571872 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.571884 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.571892 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.571916 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.571921 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.571925 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.571929 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.571934 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.571937 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.571952 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.571957 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.571961 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.571969 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.571973 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.571982 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.571985 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.571988 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.571994 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.572000 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.572003 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.572006 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.572009 80                               Options.ttl: 2592000
2025/08/28-06:43:08.572039 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.572045 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.572048 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.572053 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.572055 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.572067 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.572071 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.572080 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.572087 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.572090 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.572099 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.572105 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.572108 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601973 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601984 80           Options.merge_operator: None
2025/08/28-06:43:08.601992 80        Options.compaction_filter: None
2025/08/28-06:43:08.601997 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.602000 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.602003 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.602007 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.602086 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c18003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c18003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.602095 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.602099 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.602103 80          Options.compression: Snappy
2025/08/28-06:43:08.602106 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.602109 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.602112 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.602115 80             Options.num_levels: 7
2025/08/28-06:43:08.602119 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.602124 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.602127 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.602131 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.602134 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.602137 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.602140 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.602144 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.602147 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602150 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.602153 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602156 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602159 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.602163 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.602167 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.602171 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.602174 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.602177 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602180 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602183 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.602185 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602192 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.602195 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.602198 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.602202 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.602205 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.602213 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602218 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602223 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602228 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602232 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602235 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602238 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602240 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602243 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602246 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602249 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602252 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602255 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602258 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602261 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602264 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602267 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602271 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602275 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602278 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602281 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602284 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602287 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602290 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602294 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602297 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602301 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602310 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602314 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602322 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602327 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602332 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602335 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602338 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.602341 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602344 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602347 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602350 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602353 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602356 80                               Options.ttl: 2592000
2025/08/28-06:43:08.602359 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602362 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602365 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602369 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.602372 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.602375 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602378 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602381 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602385 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602389 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602392 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602396 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602400 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.619059 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.619071 80           Options.merge_operator: None
2025/08/28-06:43:08.619075 80        Options.compaction_filter: None
2025/08/28-06:43:08.619079 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.619082 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.619086 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.619090 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.619145 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c18005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c180061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.619151 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.619155 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.619159 80          Options.compression: Snappy
2025/08/28-06:43:08.619164 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.619167 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.619171 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.619174 80             Options.num_levels: 7
2025/08/28-06:43:08.619178 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.619185 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.619189 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.619196 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.619200 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.619204 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.619207 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619211 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619215 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619219 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.619223 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619226 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619230 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.619233 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.619237 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.619240 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619244 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619247 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619251 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619254 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.619258 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619262 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.619265 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.619269 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.619272 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.619276 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.619279 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.619283 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.619288 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.619293 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.619297 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.619300 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.619303 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.619306 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.619310 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.619313 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.619317 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.619320 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.619323 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.619327 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.619331 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.619335 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.619338 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.619342 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.619348 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.619352 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.619355 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.619358 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.619362 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.619365 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.619369 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.619373 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.619378 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.619388 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.619392 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.619395 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.619402 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.619406 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.619410 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.619413 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.619416 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.619420 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.619423 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.619426 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.619429 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.619433 80                               Options.ttl: 2592000
2025/08/28-06:43:08.619436 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.619440 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.619443 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.619446 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.619450 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.619453 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.619457 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.619460 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.619465 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.619469 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.619473 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.619476 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.619480 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.633554 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.633562 80           Options.merge_operator: None
2025/08/28-06:43:08.633565 80        Options.compaction_filter: None
2025/08/28-06:43:08.633568 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.633570 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.633573 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.633576 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.633622 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c18008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c18008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.633626 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.633629 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.633633 80          Options.compression: Snappy
2025/08/28-06:43:08.633635 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.633638 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.633641 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.633643 80             Options.num_levels: 7
2025/08/28-06:43:08.633646 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.633649 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.633651 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.633654 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.633657 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.633660 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.633662 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.633665 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.633667 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.633670 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.633673 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.633676 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.633678 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.633681 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.633684 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.633687 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.633689 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.633692 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.633715 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.633718 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.633721 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.633724 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.633726 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.633729 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.633732 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.633735 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.633738 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.633741 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.633745 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.633749 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.633752 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.633755 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.633758 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.633760 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.633763 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.633765 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.633768 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.633771 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.633774 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.633776 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.633779 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.633782 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.633785 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.633788 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.633791 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.633794 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.633797 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.633799 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.633802 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.633805 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.633807 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.633810 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.633813 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.633821 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.633824 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.633827 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.633830 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.633833 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.633836 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.633838 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.633841 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.633844 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.633846 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.633849 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.633851 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.633854 80                               Options.ttl: 2592000
2025/08/28-06:43:08.633856 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.633859 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.633862 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.633864 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.633867 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.633875 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.633878 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.633880 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.633884 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.633887 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.633891 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.633893 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.633896 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.646783 80               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.646789 80           Options.merge_operator: None
2025/08/28-06:43:08.646792 80        Options.compaction_filter: None
2025/08/28-06:43:08.646795 80        Options.compaction_filter_factory: None
2025/08/28-06:43:08.646797 80  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.646799 80         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.646801 80            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.647022 80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1800ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1800aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.647027 80        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.647029 80  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.647031 80          Options.compression: Snappy
2025/08/28-06:43:08.647034 80                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.647035 80       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.647037 80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.647039 80             Options.num_levels: 7
2025/08/28-06:43:08.647041 80        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.647043 80     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.647045 80     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.647047 80            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.647049 80                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.647051 80               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.647053 80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.647055 80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.647057 80         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.647059 80                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.647061 80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.647063 80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.647065 80            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.647067 80                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.647069 80               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.647071 80         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.647073 80         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.647075 80         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.647077 80         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.647078 80                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.647080 80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.647082 80      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.647084 80          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.647086 80              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.647088 80                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.647090 80             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.647092 80                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.647094 80 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.647097 80          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.647100 80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.647103 80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.647104 80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.647107 80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.647108 80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.647110 80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.647112 80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.647114 80       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.647116 80                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.647117 80   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.647119 80                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.647121 80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.647123 80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.647125 80                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.647127 80                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.647129 80                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.647131 80 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.647133 80 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.647135 80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.647137 80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.647139 80 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.647141 80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.647143 80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.647145 80 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.647151 80                   Options.table_properties_collectors: 
2025/08/28-06:43:08.647153 80                   Options.inplace_update_support: 0
2025/08/28-06:43:08.647155 80                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.647157 80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.647160 80               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.647161 80   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.647163 80                           Options.bloom_locality: 0
2025/08/28-06:43:08.647165 80                    Options.max_successive_merges: 0
2025/08/28-06:43:08.647167 80                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.647168 80                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.647170 80                Options.force_consistency_checks: 1
2025/08/28-06:43:08.647172 80                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.647174 80                               Options.ttl: 2592000
2025/08/28-06:43:08.647175 80          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.647177 80  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.647179 80    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.647181 80                       Options.enable_blob_files: false
2025/08/28-06:43:08.647183 80                           Options.min_blob_size: 0
2025/08/28-06:43:08.647184 80                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.647186 80                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.647188 80          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.647190 80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.647192 80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.647195 80          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.647197 80                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.647199 80 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.663413 80 DB pointer 0x7f3c18014700
