2025/08/28-06:43:08.550319 75 RocksDB version: 8.1.1
2025/08/28-06:43:08.550625 75 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550635 75 DB SUMMARY
2025/08/28-06:43:08.550638 75 DB Session ID:  A875X3AMNRT93RQW6NDE
2025/08/28-06:43:08.550685 75 SST files in ./storage/collections/example_collection/1/segments/a7874958-51fb-4d51-a795-6181057e7de8 dir, Total Num: 0, files: 
2025/08/28-06:43:08.550690 75 Write Ahead Log file in ./storage/collections/example_collection/1/segments/a7874958-51fb-4d51-a795-6181057e7de8: 
2025/08/28-06:43:08.550762 75                         Options.error_if_exists: 0
2025/08/28-06:43:08.550778 75                       Options.create_if_missing: 1
2025/08/28-06:43:08.550787 75                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550794 75             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550809 75                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550817 75        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550831 75                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550845 75                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550890 75                                Options.info_log: 0x7f3c24010540
2025/08/28-06:43:08.550901 75                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550906 75                              Options.statistics: (nil)
2025/08/28-06:43:08.550910 75                               Options.use_fsync: 0
2025/08/28-06:43:08.550914 75                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550935 75                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550940 75                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550954 75                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550958 75                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550962 75                         Options.allow_fallocate: 1
2025/08/28-06:43:08.550972 75                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.550976 75                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.550979 75                        Options.use_direct_reads: 0
2025/08/28-06:43:08.550987 75                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.550991 75          Options.create_missing_column_families: 1
2025/08/28-06:43:08.551185 75                              Options.db_log_dir: 
2025/08/28-06:43:08.551193 75                                 Options.wal_dir: 
2025/08/28-06:43:08.551247 75                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.551255 75                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.551260 75                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.551264 75                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.551268 75             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.551271 75                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.551275 75                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.551278 75                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.551282 75                    Options.write_buffer_manager: 0x7f3c2400fae0
2025/08/28-06:43:08.551295 75         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.551300 75           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.551305 75                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.551309 75                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.551313 75     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.551317 75                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.551320 75                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.551324 75                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.551328 75                  Options.unordered_write: 0
2025/08/28-06:43:08.551337 75         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.551342 75      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.551346 75             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.551349 75            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.551356 75                               Options.row_cache: None
2025/08/28-06:43:08.551360 75                              Options.wal_filter: None
2025/08/28-06:43:08.551366 75             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.551369 75             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.551373 75             Options.two_write_queues: 0
2025/08/28-06:43:08.551378 75             Options.manual_wal_flush: 0
2025/08/28-06:43:08.551385 75             Options.wal_compression: 0
2025/08/28-06:43:08.551388 75             Options.atomic_flush: 0
2025/08/28-06:43:08.551392 75             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.551395 75                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.551398 75                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.551402 75                 Options.log_readahead_size: 0
2025/08/28-06:43:08.551409 75                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.551413 75                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.551417 75                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.551421 75            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.551425 75             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.551431 75             Options.db_host_id: __hostname__
2025/08/28-06:43:08.551443 75             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.551448 75             Options.max_background_jobs: 2
2025/08/28-06:43:08.551452 75             Options.max_background_compactions: -1
2025/08/28-06:43:08.551456 75             Options.max_subcompactions: 1
2025/08/28-06:43:08.551460 75             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.551464 75           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.551468 75             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.551471 75             Options.max_total_wal_size: 0
2025/08/28-06:43:08.551475 75             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.551479 75                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.551483 75                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.551487 75                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.551490 75                          Options.max_open_files: 256
2025/08/28-06:43:08.551512 75                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.551518 75                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.551521 75                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.551524 75       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.551528 75                  Options.max_background_flushes: -1
2025/08/28-06:43:08.551531 75 Compression algorithms supported:
2025/08/28-06:43:08.551535 75 	kZSTD supported: 0
2025/08/28-06:43:08.551540 75 	kXpressCompression supported: 0
2025/08/28-06:43:08.551544 75 	kBZip2Compression supported: 0
2025/08/28-06:43:08.551547 75 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.551551 75 	kLZ4Compression supported: 0
2025/08/28-06:43:08.551555 75 	kZlibCompression supported: 0
2025/08/28-06:43:08.551558 75 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.551562 75 	kSnappyCompression supported: 1
2025/08/28-06:43:08.551568 75 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.551572 75 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571320 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.571338 75           Options.merge_operator: None
2025/08/28-06:43:08.571343 75        Options.compaction_filter: None
2025/08/28-06:43:08.571351 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.571355 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.571360 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.571364 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.571447 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2400d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2400d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.571454 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.571459 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.571467 75          Options.compression: Snappy
2025/08/28-06:43:08.571471 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.571475 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.571479 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.571483 75             Options.num_levels: 7
2025/08/28-06:43:08.571487 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.571491 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.571495 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.571500 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.571505 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.571509 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.571513 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571517 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571522 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571530 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.571537 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571541 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571550 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.571554 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.571561 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.571567 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571577 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571581 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571590 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571594 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.571606 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571612 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.571616 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.571643 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.571654 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.571660 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.571664 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.571669 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.571676 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.571681 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.571686 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.571690 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.571711 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.571716 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.571719 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.571723 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.571728 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.571732 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.571737 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.571741 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.571745 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.571750 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.571754 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.571764 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.571772 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.571777 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.571782 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.571787 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.571791 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.571798 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.571802 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.571806 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.571809 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.571824 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.571831 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.571834 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.571844 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.571850 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.571854 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.571864 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.571871 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.571875 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.571883 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.571886 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.571892 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.571902 75                               Options.ttl: 2592000
2025/08/28-06:43:08.571905 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.571910 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.571916 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.571922 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.571931 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.571939 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.571946 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.571960 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.571967 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.571972 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.571977 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.571981 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.571990 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.603988 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.603997 75           Options.merge_operator: None
2025/08/28-06:43:08.604001 75        Options.compaction_filter: None
2025/08/28-06:43:08.604004 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.604007 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.604011 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.604013 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.604073 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c24003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c24003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.604078 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.604081 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.604085 75          Options.compression: Snappy
2025/08/28-06:43:08.604089 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.604092 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.604095 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.604097 75             Options.num_levels: 7
2025/08/28-06:43:08.604101 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.604104 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.604107 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.604109 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.604113 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.604116 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.604118 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.604121 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.604124 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.604127 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.604131 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.604133 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.604136 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.604140 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.604143 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.604147 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.604150 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.604153 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.604156 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.604159 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.604162 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.604165 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.604168 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.604172 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.604175 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.604178 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.604181 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.604184 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.604189 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.604194 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.604197 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.604200 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.604207 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.604210 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.604213 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.604216 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.604219 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.604222 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.604225 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.604228 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.604231 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.604234 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.604237 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.604241 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.604244 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.604247 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.604250 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.604253 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.604256 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.604259 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.604263 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.604266 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.604269 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.604277 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.604280 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.604283 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.604287 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.604291 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.604294 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.604297 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.604300 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.604306 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.604309 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.604312 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.604315 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.604318 75                               Options.ttl: 2592000
2025/08/28-06:43:08.604321 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.604324 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.604328 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.604331 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.604334 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.604337 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.604340 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.604343 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.604347 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.604351 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.604354 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.604358 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.604361 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.619788 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.619796 75           Options.merge_operator: None
2025/08/28-06:43:08.619800 75        Options.compaction_filter: None
2025/08/28-06:43:08.619804 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.619807 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.619811 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.619815 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.619866 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c24005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c240061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.619872 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.619876 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.619880 75          Options.compression: Snappy
2025/08/28-06:43:08.619883 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.619887 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.619890 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.619893 75             Options.num_levels: 7
2025/08/28-06:43:08.619897 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.619901 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.619904 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.619907 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.619911 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.619915 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.619919 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619922 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619926 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619930 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.619934 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619938 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619942 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.619945 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.619949 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.619969 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619973 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619976 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619980 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619983 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.619987 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619990 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.619994 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.619997 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.620001 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.620004 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.620007 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.620011 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.620016 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.620021 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.620025 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.620028 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.620032 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.620035 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.620038 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.620041 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.620045 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.620049 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.620052 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.620055 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.620058 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.620061 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.620065 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.620069 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.620073 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.620077 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.620081 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.620084 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.620087 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.620091 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.620094 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.620098 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.620101 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.620110 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.620113 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.620117 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.620121 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.620125 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.620129 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.620132 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.620136 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.620139 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.620142 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.620146 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.620149 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.620152 75                               Options.ttl: 2592000
2025/08/28-06:43:08.620155 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.620159 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.620162 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.620165 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.620169 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.620172 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.620176 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.620179 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.620183 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.620187 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.620191 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.620194 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.620197 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.638267 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.638270 75           Options.merge_operator: None
2025/08/28-06:43:08.638271 75        Options.compaction_filter: None
2025/08/28-06:43:08.638273 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.638274 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.638276 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.638277 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.638298 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c24008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c24008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.638300 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.638302 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.638303 75          Options.compression: Snappy
2025/08/28-06:43:08.638305 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.638306 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.638307 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.638308 75             Options.num_levels: 7
2025/08/28-06:43:08.638310 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.638311 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.638312 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.638314 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.638315 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.638316 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.638318 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638319 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638320 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638321 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.638323 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638324 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638325 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.638327 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.638328 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.638330 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638331 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638332 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638334 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638335 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.638336 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638338 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.638339 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.638340 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.638342 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.638343 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.638344 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.638346 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.638348 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.638349 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.638351 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.638352 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.638353 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.638354 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.638356 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.638357 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.638358 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.638359 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.638360 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.638362 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.638363 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.638364 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.638366 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.638367 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.638369 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.638370 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.638371 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.638372 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.638374 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.638375 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.638376 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638378 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638379 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638382 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638383 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638384 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638386 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638387 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638388 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638390 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.638391 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638392 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638394 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638395 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638396 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638397 75                               Options.ttl: 2592000
2025/08/28-06:43:08.638399 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638400 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638401 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638402 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.638403 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.638404 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638406 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638411 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638413 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638416 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638419 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638421 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638424 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.648502 75               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.648508 75           Options.merge_operator: None
2025/08/28-06:43:08.648510 75        Options.compaction_filter: None
2025/08/28-06:43:08.648512 75        Options.compaction_filter_factory: None
2025/08/28-06:43:08.648514 75  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.648516 75         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.648518 75            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.648551 75            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2400ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2400aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.648555 75        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.648559 75  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.648562 75          Options.compression: Snappy
2025/08/28-06:43:08.648564 75                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.648566 75       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.648568 75   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.648569 75             Options.num_levels: 7
2025/08/28-06:43:08.648572 75        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.648574 75     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.648576 75     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.648578 75            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.648580 75                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.648582 75               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.648584 75         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.648586 75         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.648587 75         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.648589 75                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.648591 75         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.648593 75         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.648603 75            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.648606 75                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.648607 75               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.648609 75         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.648611 75         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.648613 75         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.648615 75         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.648617 75                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.648619 75         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.648621 75      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.648623 75          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.648624 75              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.648626 75                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.649185 75             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.649194 75                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.649196 75 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.649201 75          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.649203 75 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.649205 75 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.649207 75 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.649209 75 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.649210 75 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.649212 75 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.649213 75 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.649215 75       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.649216 75                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.649218 75   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.649220 75                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.649222 75   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.649224 75   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.649225 75                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.649228 75                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.649230 75                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.649232 75 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.649234 75 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.649236 75 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.649238 75 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.649240 75 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.649241 75 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.649243 75 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.649245 75 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.649252 75                   Options.table_properties_collectors: 
2025/08/28-06:43:08.649254 75                   Options.inplace_update_support: 0
2025/08/28-06:43:08.649255 75                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.649257 75               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.649259 75               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.649260 75   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.649262 75                           Options.bloom_locality: 0
2025/08/28-06:43:08.649263 75                    Options.max_successive_merges: 0
2025/08/28-06:43:08.649265 75                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.649266 75                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.649268 75                Options.force_consistency_checks: 1
2025/08/28-06:43:08.649269 75                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.649271 75                               Options.ttl: 2592000
2025/08/28-06:43:08.649273 75          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.649274 75  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.649276 75    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.649277 75                       Options.enable_blob_files: false
2025/08/28-06:43:08.649278 75                           Options.min_blob_size: 0
2025/08/28-06:43:08.649279 75                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.649281 75                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.649283 75          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.649285 75      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.649288 75 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.649290 75          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.649292 75                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.649294 75 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.662056 75 DB pointer 0x7f3c24014700
