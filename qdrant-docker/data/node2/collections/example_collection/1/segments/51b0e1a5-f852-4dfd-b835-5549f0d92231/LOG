2025/08/28-06:43:08.550429 78 RocksDB version: 8.1.1
2025/08/28-06:43:08.550472 78 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550479 78 DB SUMMARY
2025/08/28-06:43:08.550482 78 DB Session ID:  A875X3AMNRT93RQW6NDC
2025/08/28-06:43:08.550515 78 SST files in ./storage/collections/example_collection/1/segments/51b0e1a5-f852-4dfd-b835-5549f0d92231 dir, Total Num: 0, files: 
2025/08/28-06:43:08.550523 78 Write Ahead Log file in ./storage/collections/example_collection/1/segments/51b0e1a5-f852-4dfd-b835-5549f0d92231: 
2025/08/28-06:43:08.550529 78                         Options.error_if_exists: 0
2025/08/28-06:43:08.550533 78                       Options.create_if_missing: 1
2025/08/28-06:43:08.550538 78                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550543 78             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550552 78                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550557 78        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550562 78                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550570 78                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550578 78                                Options.info_log: 0x7f3c20010540
2025/08/28-06:43:08.550597 78                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550603 78                              Options.statistics: (nil)
2025/08/28-06:43:08.550607 78                               Options.use_fsync: 0
2025/08/28-06:43:08.550612 78                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550617 78                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550622 78                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550627 78                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550631 78                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550636 78                         Options.allow_fallocate: 1
2025/08/28-06:43:08.550640 78                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.550645 78                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.550649 78                        Options.use_direct_reads: 0
2025/08/28-06:43:08.550656 78                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.550664 78          Options.create_missing_column_families: 1
2025/08/28-06:43:08.550668 78                              Options.db_log_dir: 
2025/08/28-06:43:08.550673 78                                 Options.wal_dir: 
2025/08/28-06:43:08.550678 78                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.550688 78                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.550692 78                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.550696 78                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.550714 78             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.550719 78                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.550724 78                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.550730 78                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.550737 78                    Options.write_buffer_manager: 0x7f3c2000fae0
2025/08/28-06:43:08.550746 78         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.550751 78           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.550759 78                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.550765 78                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.550774 78     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.550781 78                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.550795 78                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.550812 78                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.550816 78                  Options.unordered_write: 0
2025/08/28-06:43:08.550825 78         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.550830 78      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.550834 78             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.550845 78            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.550849 78                               Options.row_cache: None
2025/08/28-06:43:08.550853 78                              Options.wal_filter: None
2025/08/28-06:43:08.550858 78             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.550862 78             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.550866 78             Options.two_write_queues: 0
2025/08/28-06:43:08.550870 78             Options.manual_wal_flush: 0
2025/08/28-06:43:08.550874 78             Options.wal_compression: 0
2025/08/28-06:43:08.550879 78             Options.atomic_flush: 0
2025/08/28-06:43:08.550891 78             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.550901 78                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.550909 78                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.550913 78                 Options.log_readahead_size: 0
2025/08/28-06:43:08.550938 78                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.550943 78                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.550954 78                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.550970 78            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.550974 78             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.551084 78             Options.db_host_id: __hostname__
2025/08/28-06:43:08.551093 78             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.551097 78             Options.max_background_jobs: 2
2025/08/28-06:43:08.551102 78             Options.max_background_compactions: -1
2025/08/28-06:43:08.551108 78             Options.max_subcompactions: 1
2025/08/28-06:43:08.551114 78             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.551119 78           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.551123 78             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.551127 78             Options.max_total_wal_size: 0
2025/08/28-06:43:08.551132 78             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.551136 78                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.551146 78                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.551150 78                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.551154 78                          Options.max_open_files: 256
2025/08/28-06:43:08.551158 78                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.551163 78                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.551167 78                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.551172 78       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.551176 78                  Options.max_background_flushes: -1
2025/08/28-06:43:08.551180 78 Compression algorithms supported:
2025/08/28-06:43:08.551184 78 	kZSTD supported: 0
2025/08/28-06:43:08.551188 78 	kXpressCompression supported: 0
2025/08/28-06:43:08.551193 78 	kBZip2Compression supported: 0
2025/08/28-06:43:08.551198 78 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.551203 78 	kLZ4Compression supported: 0
2025/08/28-06:43:08.551208 78 	kZlibCompression supported: 0
2025/08/28-06:43:08.551212 78 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.551219 78 	kSnappyCompression supported: 1
2025/08/28-06:43:08.551224 78 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.551228 78 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571437 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.571444 78           Options.merge_operator: None
2025/08/28-06:43:08.571448 78        Options.compaction_filter: None
2025/08/28-06:43:08.571455 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.571459 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.571464 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.571468 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.571568 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2000d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2000d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.571579 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.571583 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.571591 78          Options.compression: Snappy
2025/08/28-06:43:08.571595 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.571617 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.571626 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.571653 78             Options.num_levels: 7
2025/08/28-06:43:08.571658 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.571662 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.571683 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.571688 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.571692 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.571695 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.571700 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571717 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571722 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571726 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.571729 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571732 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571739 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.571744 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.571752 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.571755 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571762 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571765 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571768 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571772 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.571778 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571781 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.571786 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.571793 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.571797 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.571801 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.571804 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.571808 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.571814 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.571819 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.571824 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.571828 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.571833 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.571836 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.571845 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.571848 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.571853 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.571864 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.571870 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.571874 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.571883 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.571902 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.571905 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.571912 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.571922 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.571940 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.571947 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.571953 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.571959 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.571965 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.571969 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.571973 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.571991 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.572010 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.572018 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.572022 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.572028 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.572035 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.572039 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.572043 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.572048 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.572057 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.572061 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.572064 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.572069 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.572073 78                               Options.ttl: 2592000
2025/08/28-06:43:08.572076 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.572080 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.572091 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.572098 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.572101 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.572109 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.572115 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.572123 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.572128 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.572133 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.572137 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.572141 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.572149 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.590584 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.590594 78           Options.merge_operator: None
2025/08/28-06:43:08.590598 78        Options.compaction_filter: None
2025/08/28-06:43:08.590601 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.590605 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.590609 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.590612 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.590675 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c20003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c20003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.590681 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.590685 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.590689 78          Options.compression: Snappy
2025/08/28-06:43:08.590693 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.590696 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.590700 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.590817 78             Options.num_levels: 7
2025/08/28-06:43:08.590821 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.590825 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.590829 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.590833 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.590838 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.590841 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.590845 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590848 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590852 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590856 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.590860 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.590863 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590867 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.590880 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.590883 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.590887 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.590890 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.590894 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.590897 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.590901 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.590904 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.590907 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.590910 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.590914 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.590917 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.590921 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.590924 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.590927 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.590933 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.590938 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.590942 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.590945 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.590948 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.590952 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.590955 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.590958 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.590962 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.590965 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.590969 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.590972 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.590976 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.590980 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.590983 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.590988 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.590996 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.590999 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.591003 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.591007 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.591010 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.591014 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.591017 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.591021 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.591024 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.591034 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.591038 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.591042 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.591047 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.591051 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.591054 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.591058 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.591061 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.591065 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.591068 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.591072 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.591075 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.591078 78                               Options.ttl: 2592000
2025/08/28-06:43:08.591082 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.591085 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.591089 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.591092 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.591096 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.591100 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.591107 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.591111 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.591114 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.591118 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.591122 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.591126 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.591130 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.610216 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.610222 78           Options.merge_operator: None
2025/08/28-06:43:08.610224 78        Options.compaction_filter: None
2025/08/28-06:43:08.610226 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.610228 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.610230 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.610232 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.610272 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c20005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c200061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.610275 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.610277 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.610279 78          Options.compression: Snappy
2025/08/28-06:43:08.610282 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.610284 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.610286 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.610288 78             Options.num_levels: 7
2025/08/28-06:43:08.610290 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.610292 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.610294 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.610296 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.610298 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.610300 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.610302 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.610304 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.610306 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.610308 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.610311 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.610314 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.610316 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.610318 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.610320 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.610322 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.610324 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.610326 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.610328 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.610331 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.610332 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.610334 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.610336 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.610339 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.610341 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.610343 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.610345 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.610346 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.610350 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.610353 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.610355 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.610357 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.610358 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.610360 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.610362 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.610364 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.610366 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.610368 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.610370 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.610372 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.610374 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.610376 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.610377 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.610379 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.610381 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.610382 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.610384 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.610386 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.610387 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.610389 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.610391 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.610392 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.610394 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.610400 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.610402 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.610403 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.610405 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.610407 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.610408 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.610410 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.610412 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.610413 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.610415 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.610417 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.610418 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.610420 78                               Options.ttl: 2592000
2025/08/28-06:43:08.610422 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.610424 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.610425 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.610427 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.610428 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.610430 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.610431 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.610433 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.610435 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.610438 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.610440 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.610441 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.610443 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.624266 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.624278 78           Options.merge_operator: None
2025/08/28-06:43:08.624281 78        Options.compaction_filter: None
2025/08/28-06:43:08.624284 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.624288 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.624291 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.624295 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.624357 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c20008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c20008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.624361 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.624364 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.624368 78          Options.compression: Snappy
2025/08/28-06:43:08.624371 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.624374 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.624377 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.624381 78             Options.num_levels: 7
2025/08/28-06:43:08.624385 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.624388 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.624391 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.624395 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.624398 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.624402 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.624404 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.624408 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.624411 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.624415 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.624418 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.624421 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.624424 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.624428 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.624431 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.624434 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.624438 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.624441 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.624444 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.624447 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.624450 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.624453 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.624456 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.624460 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.624463 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.624466 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.624469 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.624473 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.624479 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.624483 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.624487 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.624490 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.624493 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.624496 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.624499 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.624502 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.624505 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.624508 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.624512 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.624515 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.624518 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.624522 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.624525 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.624528 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.624531 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.624535 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.624543 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.624544 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.624546 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.624547 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.624548 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.624550 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.624551 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.624557 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.624559 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.624560 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.624562 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.624564 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.624565 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.624567 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.624568 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.624570 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.624571 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.624573 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.624574 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.624575 78                               Options.ttl: 2592000
2025/08/28-06:43:08.624576 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.624578 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.624579 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.624581 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.624582 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.624583 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.624585 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.624586 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.624587 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.624589 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.624591 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.624592 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.624593 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637747 78               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637754 78           Options.merge_operator: None
2025/08/28-06:43:08.637757 78        Options.compaction_filter: None
2025/08/28-06:43:08.637760 78        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637763 78  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637765 78         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637768 78            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637808 78            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2000ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2000aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637812 78        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637815 78  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637819 78          Options.compression: Snappy
2025/08/28-06:43:08.637821 78                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637824 78       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637827 78   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637829 78             Options.num_levels: 7
2025/08/28-06:43:08.637832 78        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637834 78     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637837 78     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637839 78            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.637842 78                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.637845 78               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.637847 78         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637850 78         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637853 78         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637855 78                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.637858 78         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637861 78         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637863 78            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.637866 78                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.637869 78               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.637871 78         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.637874 78         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.637876 78         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.637879 78         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.637881 78                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.637884 78         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.637886 78      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.637889 78          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.637891 78              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.637894 78                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.637897 78             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.637899 78                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.637902 78 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.637906 78          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.637910 78 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.637913 78 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.637915 78 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.637919 78 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.637922 78 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.637924 78 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.637927 78 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.637929 78       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.637932 78                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.637934 78   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.637936 78                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.637939 78   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.637942 78   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.637944 78                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.637948 78                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.637951 78                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.637954 78 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.637956 78 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.637959 78 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.637961 78 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.637964 78 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.637967 78 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.637970 78 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.637972 78 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.637980 78                   Options.table_properties_collectors: 
2025/08/28-06:43:08.637982 78                   Options.inplace_update_support: 0
2025/08/28-06:43:08.637985 78                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.637988 78               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.637991 78               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.637993 78   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.637996 78                           Options.bloom_locality: 0
2025/08/28-06:43:08.637998 78                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638001 78                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638007 78                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638008 78                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638010 78                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638011 78                               Options.ttl: 2592000
2025/08/28-06:43:08.638012 78          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638014 78  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638015 78    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638016 78                       Options.enable_blob_files: false
2025/08/28-06:43:08.638017 78                           Options.min_blob_size: 0
2025/08/28-06:43:08.638019 78                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638020 78                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638022 78          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638024 78      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638025 78 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638027 78          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638028 78                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638030 78 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.658037 78 DB pointer 0x7f3c20014700
