2025/08/28-06:43:08.550320 77 RocksDB version: 8.1.1
2025/08/28-06:43:08.550553 77 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550559 77 DB SUMMARY
2025/08/28-06:43:08.550564 77 DB Session ID:  A875X3AMNRT93RQW6NDH
2025/08/28-06:43:08.550605 77 SST files in ./storage/collections/example_collection/1/segments/8b2cd681-bfdc-4a8f-ae45-f3e7ce36c8f6 dir, Total Num: 0, files: 
2025/08/28-06:43:08.550611 77 Write Ahead Log file in ./storage/collections/example_collection/1/segments/8b2cd681-bfdc-4a8f-ae45-f3e7ce36c8f6: 
2025/08/28-06:43:08.550619 77                         Options.error_if_exists: 0
2025/08/28-06:43:08.550624 77                       Options.create_if_missing: 1
2025/08/28-06:43:08.550628 77                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550633 77             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550643 77                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550648 77        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550653 77                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550658 77                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550665 77                                Options.info_log: 0x7f3c1c010540
2025/08/28-06:43:08.550673 77                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550686 77                              Options.statistics: (nil)
2025/08/28-06:43:08.550691 77                               Options.use_fsync: 0
2025/08/28-06:43:08.550716 77                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550721 77                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550726 77                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550735 77                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550744 77                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550749 77                         Options.allow_fallocate: 1
2025/08/28-06:43:08.550758 77                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.550765 77                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.550768 77                        Options.use_direct_reads: 0
2025/08/28-06:43:08.550781 77                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.550795 77          Options.create_missing_column_families: 1
2025/08/28-06:43:08.550802 77                              Options.db_log_dir: 
2025/08/28-06:43:08.550806 77                                 Options.wal_dir: 
2025/08/28-06:43:08.550812 77                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.550817 77                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.550822 77                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.550826 77                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.550831 77             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.550846 77                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.550856 77                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.550860 77                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.550865 77                    Options.write_buffer_manager: 0x7f3c1c00fae0
2025/08/28-06:43:08.550878 77         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.550892 77           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.550897 77                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.550935 77                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.550941 77     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.550945 77                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.550950 77                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.550954 77                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.550966 77                  Options.unordered_write: 0
2025/08/28-06:43:08.550975 77         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.550979 77      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.550990 77             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.550993 77            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.550999 77                               Options.row_cache: None
2025/08/28-06:43:08.551002 77                              Options.wal_filter: None
2025/08/28-06:43:08.551005 77             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.551009 77             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.551013 77             Options.two_write_queues: 0
2025/08/28-06:43:08.551016 77             Options.manual_wal_flush: 0
2025/08/28-06:43:08.551020 77             Options.wal_compression: 0
2025/08/28-06:43:08.551024 77             Options.atomic_flush: 0
2025/08/28-06:43:08.551028 77             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.551032 77                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.551037 77                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.551041 77                 Options.log_readahead_size: 0
2025/08/28-06:43:08.551046 77                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.551050 77                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.551054 77                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.551059 77            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.551063 77             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.551066 77             Options.db_host_id: __hostname__
2025/08/28-06:43:08.551074 77             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.551078 77             Options.max_background_jobs: 2
2025/08/28-06:43:08.551082 77             Options.max_background_compactions: -1
2025/08/28-06:43:08.551087 77             Options.max_subcompactions: 1
2025/08/28-06:43:08.551091 77             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.551097 77           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.551101 77             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.551107 77             Options.max_total_wal_size: 0
2025/08/28-06:43:08.551114 77             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.551120 77                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.551127 77                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.551131 77                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.551136 77                          Options.max_open_files: 256
2025/08/28-06:43:08.551140 77                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.551145 77                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.551149 77                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.551154 77       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.551161 77                  Options.max_background_flushes: -1
2025/08/28-06:43:08.551165 77 Compression algorithms supported:
2025/08/28-06:43:08.551170 77 	kZSTD supported: 0
2025/08/28-06:43:08.551175 77 	kXpressCompression supported: 0
2025/08/28-06:43:08.551179 77 	kBZip2Compression supported: 0
2025/08/28-06:43:08.551184 77 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.551188 77 	kLZ4Compression supported: 0
2025/08/28-06:43:08.551192 77 	kZlibCompression supported: 0
2025/08/28-06:43:08.551198 77 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.551204 77 	kSnappyCompression supported: 1
2025/08/28-06:43:08.551211 77 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.551216 77 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571320 77               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.571350 77           Options.merge_operator: None
2025/08/28-06:43:08.571354 77        Options.compaction_filter: None
2025/08/28-06:43:08.571362 77        Options.compaction_filter_factory: None
2025/08/28-06:43:08.571366 77  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.571370 77         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.571374 77            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.571455 77            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1c00d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1c00d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.571461 77        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.571466 77  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.571470 77          Options.compression: Snappy
2025/08/28-06:43:08.571474 77                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.571478 77       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.571481 77   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.571485 77             Options.num_levels: 7
2025/08/28-06:43:08.571489 77        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.571492 77     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.571496 77     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.571500 77            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.571505 77                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.571509 77               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.571514 77         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571517 77         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571523 77         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571536 77                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.571541 77         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571548 77         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571559 77            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.571570 77                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.571574 77               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.571578 77         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571581 77         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571606 77         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571616 77         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571624 77                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.571631 77         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571634 77      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.571640 77          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.571657 77              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.571678 77                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.571682 77             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.571686 77                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.571690 77 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.571698 77          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.571717 77 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.571723 77 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.571728 77 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.571732 77 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.571735 77 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.571739 77 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.571745 77 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.571751 77       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.571754 77                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.571771 77   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.571775 77                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.571778 77   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.571782 77   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.571787 77                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.571791 77                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.571807 77                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.571813 77 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.571815 77 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.571818 77 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.571821 77 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.571827 77 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.571830 77 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.571834 77 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.571844 77 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.571852 77                   Options.table_properties_collectors: 
2025/08/28-06:43:08.571855 77                   Options.inplace_update_support: 0
2025/08/28-06:43:08.571873 77                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.571879 77               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.571883 77               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.571888 77   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.571892 77                           Options.bloom_locality: 0
2025/08/28-06:43:08.571902 77                    Options.max_successive_merges: 0
2025/08/28-06:43:08.571906 77                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.571909 77                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.571918 77                Options.force_consistency_checks: 1
2025/08/28-06:43:08.571922 77                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.571935 77                               Options.ttl: 2592000
2025/08/28-06:43:08.571941 77          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.571945 77  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.571949 77    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.571951 77                       Options.enable_blob_files: false
2025/08/28-06:43:08.571955 77                           Options.min_blob_size: 0
2025/08/28-06:43:08.571961 77                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.571967 77                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.571974 77          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.571979 77      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.571983 77 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.571987 77          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.571991 77                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.571999 77 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.609978 77               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.609985 77           Options.merge_operator: None
2025/08/28-06:43:08.609987 77        Options.compaction_filter: None
2025/08/28-06:43:08.609989 77        Options.compaction_filter_factory: None
2025/08/28-06:43:08.609990 77  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.609992 77         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.609994 77            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.610031 77            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1c003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1c003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.610034 77        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.610036 77  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.610038 77          Options.compression: Snappy
2025/08/28-06:43:08.610040 77                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.610042 77       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.610043 77   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.610045 77             Options.num_levels: 7
2025/08/28-06:43:08.610047 77        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.610048 77     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.610050 77     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.610052 77            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.610054 77                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.610056 77               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.610057 77         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.610059 77         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.610061 77         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.610063 77                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.610064 77         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.610066 77         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.610068 77            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.610069 77                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.610071 77               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.610073 77         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.610074 77         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.610076 77         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.610078 77         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.610079 77                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.610083 77         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.610085 77      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.610087 77          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.610088 77              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.610090 77                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.610092 77             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.610094 77                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.610095 77 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.610099 77          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.610101 77 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.610148 77 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.610149 77 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.610151 77 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.610152 77 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.610154 77 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.610156 77 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.610157 77       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.610159 77                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.610232 77   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.610233 77                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.610235 77   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.610237 77   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.610239 77                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.610241 77                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.610243 77                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.610245 77 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.610247 77 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.610248 77 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.610250 77 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.610255 77 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.610257 77 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.610258 77 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.610260 77 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.610269 77                   Options.table_properties_collectors: 
2025/08/28-06:43:08.610271 77                   Options.inplace_update_support: 0
2025/08/28-06:43:08.610273 77                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.610275 77               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.610277 77               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.610279 77   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.610281 77                           Options.bloom_locality: 0
2025/08/28-06:43:08.610282 77                    Options.max_successive_merges: 0
2025/08/28-06:43:08.610284 77                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.610286 77                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.610287 77                Options.force_consistency_checks: 1
2025/08/28-06:43:08.610289 77                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.610291 77                               Options.ttl: 2592000
2025/08/28-06:43:08.610292 77          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.610294 77  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.610296 77    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.610297 77                       Options.enable_blob_files: false
2025/08/28-06:43:08.610299 77                           Options.min_blob_size: 0
2025/08/28-06:43:08.610301 77                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.610303 77                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.610304 77          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.610307 77      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.610309 77 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.610311 77          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.610313 77                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.610314 77 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.625984 77               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.625996 77           Options.merge_operator: None
2025/08/28-06:43:08.626000 77        Options.compaction_filter: None
2025/08/28-06:43:08.626003 77        Options.compaction_filter_factory: None
2025/08/28-06:43:08.626007 77  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.626011 77         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.626014 77            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.626095 77            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1c005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1c0061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.626102 77        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.626105 77  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.626110 77          Options.compression: Snappy
2025/08/28-06:43:08.626113 77                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.626116 77       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.626119 77   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.626122 77             Options.num_levels: 7
2025/08/28-06:43:08.626125 77        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.626129 77     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.626132 77     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.626135 77            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.626139 77                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.626142 77               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.626145 77         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.626148 77         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.626152 77         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.626155 77                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.626158 77         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.626161 77         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.626164 77            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.626168 77                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.626171 77               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.626175 77         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.626178 77         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.626181 77         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.626183 77         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.626187 77                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.626190 77         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.626193 77      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.626196 77          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.626199 77              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.626202 77                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.626206 77             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.626209 77                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.626212 77 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.626218 77          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.626222 77 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.626226 77 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.626228 77 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.626232 77 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.626234 77 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.626237 77 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.626240 77 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.626243 77       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.626246 77                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.626249 77   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.626252 77                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.626255 77   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.626258 77   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.626261 77                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.626265 77                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.626268 77                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.626271 77 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.626274 77 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.626277 77 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.626280 77 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.626284 77 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.626287 77 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.626290 77 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.626293 77 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.626305 77                   Options.table_properties_collectors: 
2025/08/28-06:43:08.626308 77                   Options.inplace_update_support: 0
2025/08/28-06:43:08.626311 77                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.626316 77               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.626320 77               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.626323 77   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.626326 77                           Options.bloom_locality: 0
2025/08/28-06:43:08.626329 77                    Options.max_successive_merges: 0
2025/08/28-06:43:08.626332 77                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.626335 77                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.626338 77                Options.force_consistency_checks: 1
2025/08/28-06:43:08.626341 77                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.626344 77                               Options.ttl: 2592000
2025/08/28-06:43:08.626347 77          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.626349 77  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.626352 77    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.626355 77                       Options.enable_blob_files: false
2025/08/28-06:43:08.626358 77                           Options.min_blob_size: 0
2025/08/28-06:43:08.626361 77                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.626365 77                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.626367 77          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.626371 77      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.626375 77 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.626378 77          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.626381 77                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.626385 77 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.642896 77               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.642903 77           Options.merge_operator: None
2025/08/28-06:43:08.642906 77        Options.compaction_filter: None
2025/08/28-06:43:08.642909 77        Options.compaction_filter_factory: None
2025/08/28-06:43:08.642911 77  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.642916 77         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.642918 77            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.642960 77            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1c008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1c008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.642965 77        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.642967 77  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.642970 77          Options.compression: Snappy
2025/08/28-06:43:08.642972 77                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.642974 77       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.642976 77   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.642978 77             Options.num_levels: 7
2025/08/28-06:43:08.642981 77        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.642984 77     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.642986 77     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.642988 77            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.642991 77                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.642993 77               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.642996 77         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.642998 77         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.643000 77         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.643003 77                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.643005 77         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.643008 77         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.643010 77            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.643012 77                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.643015 77               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.643017 77         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.643019 77         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.643022 77         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.643025 77         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.643027 77                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.643029 77         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.643031 77      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.643033 77          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.643036 77              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.643038 77                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.643041 77             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.643044 77                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.643046 77 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.643050 77          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.643053 77 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.643056 77 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.643058 77 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.643060 77 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.643063 77 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.643065 77 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.643067 77 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.643069 77       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.643072 77                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.643074 77   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.643076 77                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.643078 77   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.643081 77   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.643083 77                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.643086 77                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.643089 77                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.643091 77 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.643093 77 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.643095 77 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.643098 77 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.643100 77 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.643103 77 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.643105 77 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.643107 77 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.643114 77                   Options.table_properties_collectors: 
2025/08/28-06:43:08.643116 77                   Options.inplace_update_support: 0
2025/08/28-06:43:08.643118 77                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.643121 77               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.643124 77               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.643126 77   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.643128 77                           Options.bloom_locality: 0
2025/08/28-06:43:08.643130 77                    Options.max_successive_merges: 0
2025/08/28-06:43:08.643132 77                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.643134 77                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.643136 77                Options.force_consistency_checks: 1
2025/08/28-06:43:08.643138 77                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.643140 77                               Options.ttl: 2592000
2025/08/28-06:43:08.643142 77          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.643144 77  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.643146 77    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.643149 77                       Options.enable_blob_files: false
2025/08/28-06:43:08.643151 77                           Options.min_blob_size: 0
2025/08/28-06:43:08.643153 77                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.643155 77                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.643158 77          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.643160 77      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.643163 77 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.643166 77          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.643169 77                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.643171 77 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.656542 77               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.656548 77           Options.merge_operator: None
2025/08/28-06:43:08.656575 77        Options.compaction_filter: None
2025/08/28-06:43:08.656578 77        Options.compaction_filter_factory: None
2025/08/28-06:43:08.656580 77  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.656582 77         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.656584 77            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.656619 77            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1c00ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1c00aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.656623 77        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.656625 77  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.656627 77          Options.compression: Snappy
2025/08/28-06:43:08.656629 77                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.656650 77       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.656652 77   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.656654 77             Options.num_levels: 7
2025/08/28-06:43:08.656656 77        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.656658 77     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.656660 77     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.656662 77            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.656664 77                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.656666 77               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.656668 77         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.656669 77         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.656671 77         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.656673 77                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.656675 77         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.656677 77         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.656678 77            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.656680 77                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.656682 77               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.656684 77         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.656685 77         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.656687 77         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.656688 77         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.656690 77                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.656691 77         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.656693 77      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.656695 77          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.656697 77              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.656698 77                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.656710 77             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.656714 77                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.656715 77 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.656718 77          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.656721 77 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.656722 77 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.656724 77 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.656726 77 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.656727 77 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.656729 77 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.656730 77 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.656732 77       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.656733 77                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.656735 77   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.656737 77                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.656738 77   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.656740 77   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.656742 77                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.656743 77                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.656745 77                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.656747 77 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.656749 77 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.656750 77 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.656752 77 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.656754 77 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.656755 77 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.656757 77 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.656759 77 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.656766 77                   Options.table_properties_collectors: 
2025/08/28-06:43:08.656768 77                   Options.inplace_update_support: 0
2025/08/28-06:43:08.656769 77                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.656771 77               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.656773 77               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.656775 77   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.656777 77                           Options.bloom_locality: 0
2025/08/28-06:43:08.656778 77                    Options.max_successive_merges: 0
2025/08/28-06:43:08.656780 77                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.656781 77                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.656783 77                Options.force_consistency_checks: 1
2025/08/28-06:43:08.656784 77                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.656786 77                               Options.ttl: 2592000
2025/08/28-06:43:08.656787 77          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.656789 77  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.656790 77    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.656792 77                       Options.enable_blob_files: false
2025/08/28-06:43:08.656793 77                           Options.min_blob_size: 0
2025/08/28-06:43:08.656795 77                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.656797 77                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.656798 77          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.656800 77      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.656802 77 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.656804 77          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.656806 77                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.656808 77 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.670723 77 DB pointer 0x7f3c1c014700
