2025/08/28-06:43:08.550546 79 RocksDB version: 8.1.1
2025/08/28-06:43:08.550591 79 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550598 79 DB SUMMARY
2025/08/28-06:43:08.550602 79 DB Session ID:  A875X3AMNRT93RQW6NDD
2025/08/28-06:43:08.550670 79 SST files in ./storage/collections/example_collection/1/segments/6d601af4-0872-420c-9c37-02f4b8ef599e dir, Total Num: 0, files: 
2025/08/28-06:43:08.550677 79 Write Ahead Log file in ./storage/collections/example_collection/1/segments/6d601af4-0872-420c-9c37-02f4b8ef599e: 
2025/08/28-06:43:08.550688 79                         Options.error_if_exists: 0
2025/08/28-06:43:08.550737 79                       Options.create_if_missing: 1
2025/08/28-06:43:08.550749 79                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550762 79             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550785 79                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550791 79        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550796 79                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550831 79                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550863 79                                Options.info_log: 0x7f3c14010540
2025/08/28-06:43:08.550874 79                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550878 79                              Options.statistics: (nil)
2025/08/28-06:43:08.550906 79                               Options.use_fsync: 0
2025/08/28-06:43:08.550914 79                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550938 79                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550952 79                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550957 79                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550987 79                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550992 79                         Options.allow_fallocate: 1
2025/08/28-06:43:08.551180 79                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.551186 79                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.551193 79                        Options.use_direct_reads: 0
2025/08/28-06:43:08.551207 79                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.551212 79          Options.create_missing_column_families: 1
2025/08/28-06:43:08.551218 79                              Options.db_log_dir: 
2025/08/28-06:43:08.551222 79                                 Options.wal_dir: 
2025/08/28-06:43:08.551226 79                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.551229 79                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.551233 79                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.551237 79                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.551240 79             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.551244 79                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.551247 79                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.551253 79                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.551257 79                    Options.write_buffer_manager: 0x7f3c1400fae0
2025/08/28-06:43:08.551270 79         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.551277 79           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.551281 79                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.551285 79                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.551289 79     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.551293 79                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.551296 79                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.551300 79                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.551303 79                  Options.unordered_write: 0
2025/08/28-06:43:08.551312 79         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.551316 79      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.551320 79             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.551324 79            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.551327 79                               Options.row_cache: None
2025/08/28-06:43:08.551331 79                              Options.wal_filter: None
2025/08/28-06:43:08.551334 79             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.551338 79             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.551343 79             Options.two_write_queues: 0
2025/08/28-06:43:08.551346 79             Options.manual_wal_flush: 0
2025/08/28-06:43:08.551350 79             Options.wal_compression: 0
2025/08/28-06:43:08.551356 79             Options.atomic_flush: 0
2025/08/28-06:43:08.551360 79             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.551369 79                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.551373 79                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.551379 79                 Options.log_readahead_size: 0
2025/08/28-06:43:08.551409 79                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.551412 79                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.551417 79                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.551420 79            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.551425 79             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.551430 79             Options.db_host_id: __hostname__
2025/08/28-06:43:08.551438 79             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.551441 79             Options.max_background_jobs: 2
2025/08/28-06:43:08.551444 79             Options.max_background_compactions: -1
2025/08/28-06:43:08.551446 79             Options.max_subcompactions: 1
2025/08/28-06:43:08.551449 79             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.551451 79           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.551454 79             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.551456 79             Options.max_total_wal_size: 0
2025/08/28-06:43:08.551459 79             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.551461 79                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.551463 79                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.551466 79                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.551468 79                          Options.max_open_files: 256
2025/08/28-06:43:08.551470 79                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.551472 79                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.551475 79                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.551477 79       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.551480 79                  Options.max_background_flushes: -1
2025/08/28-06:43:08.551485 79 Compression algorithms supported:
2025/08/28-06:43:08.551488 79 	kZSTD supported: 0
2025/08/28-06:43:08.551493 79 	kXpressCompression supported: 0
2025/08/28-06:43:08.551497 79 	kBZip2Compression supported: 0
2025/08/28-06:43:08.551502 79 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.551504 79 	kLZ4Compression supported: 0
2025/08/28-06:43:08.551507 79 	kZlibCompression supported: 0
2025/08/28-06:43:08.551509 79 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.551512 79 	kSnappyCompression supported: 1
2025/08/28-06:43:08.551514 79 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.551517 79 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571994 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.572008 79           Options.merge_operator: None
2025/08/28-06:43:08.572015 79        Options.compaction_filter: None
2025/08/28-06:43:08.572023 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.572027 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.572040 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.572046 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.572124 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1400d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1400d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.572131 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.572134 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.572138 79          Options.compression: Snappy
2025/08/28-06:43:08.572141 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.572151 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.572153 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.572158 79             Options.num_levels: 7
2025/08/28-06:43:08.572162 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.572167 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.572170 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.572173 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.572176 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.572179 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.572182 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.572185 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.572188 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.572191 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.572194 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.572197 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.572200 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.572202 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.572210 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.572214 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.572216 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.572219 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.572222 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.572224 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.572227 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.572230 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.572232 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.572240 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.572243 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.572246 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.572248 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.572251 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.572256 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.572261 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.572264 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.572266 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.572269 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.572272 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.572274 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.572277 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.572279 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.572282 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.572285 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.572287 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.572290 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.572293 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.572296 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.572299 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.572307 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.572311 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.572314 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.572317 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.572319 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.572322 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.572326 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.572329 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.572332 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.572396 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.572401 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.572404 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.572408 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.572412 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.572414 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.572417 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.572420 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.572422 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.572425 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.572427 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.572430 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.572432 79                               Options.ttl: 2592000
2025/08/28-06:43:08.572435 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.572437 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.572440 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.572443 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.572445 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.572455 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.572458 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.572465 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.572470 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.572474 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.572478 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.572481 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.572484 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601782 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601792 79           Options.merge_operator: None
2025/08/28-06:43:08.601795 79        Options.compaction_filter: None
2025/08/28-06:43:08.601798 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601801 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601803 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601806 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.601872 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c14003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c14003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.601877 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.601880 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.601884 79          Options.compression: Snappy
2025/08/28-06:43:08.601887 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.601889 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.601892 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.601895 79             Options.num_levels: 7
2025/08/28-06:43:08.601898 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.601902 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.601905 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.601908 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.601912 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.601914 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.601917 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601920 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601923 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601926 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.601929 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601932 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601934 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.601938 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.601940 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.601962 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.601965 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.601970 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.601973 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.601976 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.601979 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.601984 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.601989 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.601992 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.601994 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.601997 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.602000 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602003 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602008 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602012 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602015 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602018 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602021 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602023 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602026 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602029 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602031 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602034 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602037 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602040 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602045 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602048 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602051 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602055 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602058 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602061 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602064 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602068 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602072 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602076 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602085 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602088 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602111 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602121 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602124 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602127 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602131 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602134 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602136 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602139 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.602142 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602145 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602148 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602150 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602154 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602157 79                               Options.ttl: 2592000
2025/08/28-06:43:08.602160 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602163 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602167 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602170 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.602173 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.602176 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602179 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602182 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602185 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602190 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602194 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602197 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602201 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.617437 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.617446 79           Options.merge_operator: None
2025/08/28-06:43:08.617449 79        Options.compaction_filter: None
2025/08/28-06:43:08.617453 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.617456 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.617460 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.617463 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.617521 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c14005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c140061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.617526 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.617529 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.617533 79          Options.compression: Snappy
2025/08/28-06:43:08.617536 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.617539 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.617542 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.617545 79             Options.num_levels: 7
2025/08/28-06:43:08.617549 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.617552 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.617556 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.617559 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.617562 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.617565 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.617568 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617571 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617574 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617578 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.617581 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617584 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617587 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.617590 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.617593 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.617596 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.617600 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.617602 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.617605 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.617608 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.617612 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.617615 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.617617 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.617621 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.617624 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.617627 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.617630 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.617633 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.617638 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.617643 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.617650 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.617654 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.617657 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.617660 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.617663 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.617666 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.617669 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.617674 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.617683 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.617685 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.617686 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.617687 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.617688 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.617690 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.617692 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.617693 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.617694 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.617696 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.617697 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.617698 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.617704 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.617706 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.617707 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.617712 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.617713 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.617714 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.617715 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.617717 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.617718 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.617719 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.617720 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.617722 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.617723 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.617724 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.617725 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.617726 79                               Options.ttl: 2592000
2025/08/28-06:43:08.617727 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.617728 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.617729 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.617731 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.617732 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.617734 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.617736 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.617737 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.617739 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.617741 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.617743 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.617744 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.617746 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.635255 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.635261 79           Options.merge_operator: None
2025/08/28-06:43:08.635264 79        Options.compaction_filter: None
2025/08/28-06:43:08.635267 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.635270 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.635273 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.635276 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.635318 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c14008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c14008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.635322 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.635324 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.635326 79          Options.compression: Snappy
2025/08/28-06:43:08.635328 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.635329 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.635331 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.635332 79             Options.num_levels: 7
2025/08/28-06:43:08.635334 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.635336 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.635338 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.635339 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.635341 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.635343 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.635345 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.635346 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.635348 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.635349 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.635351 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.635353 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.635354 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.635356 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.635357 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.635359 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.635361 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.635362 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.635364 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.635365 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.635367 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.635368 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.635370 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.635372 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.635373 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.635375 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.635376 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.635378 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.635382 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.635386 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.635388 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.635390 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.635392 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.635395 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.635397 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.635400 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.635402 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.635405 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.635407 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.635410 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.635412 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.635415 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.635417 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.635421 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.635424 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.635427 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.635429 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.635432 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.635434 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.635437 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.635440 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.635442 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.635445 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.635452 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.635454 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.635456 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.635459 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.635463 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.635465 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.635467 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.635469 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.635471 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.635473 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.635475 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.635476 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.635479 79                               Options.ttl: 2592000
2025/08/28-06:43:08.635481 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.635484 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.635487 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.635488 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.635490 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.635491 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.635493 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.635495 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.635497 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.635499 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.635501 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.635502 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.635504 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.646239 79               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.646245 79           Options.merge_operator: None
2025/08/28-06:43:08.646248 79        Options.compaction_filter: None
2025/08/28-06:43:08.646250 79        Options.compaction_filter_factory: None
2025/08/28-06:43:08.646252 79  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.646255 79         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.646257 79            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.646294 79            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c1400ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c1400aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.646299 79        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.646301 79  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.646303 79          Options.compression: Snappy
2025/08/28-06:43:08.646306 79                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.646308 79       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.646310 79   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.646312 79             Options.num_levels: 7
2025/08/28-06:43:08.646314 79        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.646316 79     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.646318 79     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.646321 79            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.646323 79                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.646325 79               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.646327 79         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.646330 79         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.646332 79         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.646334 79                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.646336 79         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.646338 79         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.646341 79            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.646343 79                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.646345 79               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.646347 79         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.646349 79         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.646351 79         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.646353 79         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.646355 79                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.646357 79         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.646359 79      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.646361 79          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.646364 79              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.646366 79                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.646368 79             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.646370 79                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.646372 79 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.646375 79          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.646378 79 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.646381 79 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.646383 79 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.646385 79 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.646387 79 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.646389 79 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.646390 79 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.646392 79       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.646394 79                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.646397 79   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.646399 79                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.646401 79   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.646403 79   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.646405 79                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.646408 79                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.646410 79                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.646412 79 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.646414 79 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.646416 79 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.646418 79 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.646420 79 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.646423 79 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.646425 79 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.646427 79 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.646435 79                   Options.table_properties_collectors: 
2025/08/28-06:43:08.646438 79                   Options.inplace_update_support: 0
2025/08/28-06:43:08.646439 79                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.646442 79               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.646445 79               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.646447 79   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.646449 79                           Options.bloom_locality: 0
2025/08/28-06:43:08.646451 79                    Options.max_successive_merges: 0
2025/08/28-06:43:08.646453 79                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.646454 79                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.646456 79                Options.force_consistency_checks: 1
2025/08/28-06:43:08.646458 79                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.646460 79                               Options.ttl: 2592000
2025/08/28-06:43:08.646462 79          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.646464 79  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.646466 79    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.646468 79                       Options.enable_blob_files: false
2025/08/28-06:43:08.646470 79                           Options.min_blob_size: 0
2025/08/28-06:43:08.646472 79                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.646474 79                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.646476 79          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.646478 79      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.646481 79 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.646483 79          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.646486 79                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.646488 79 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.663411 79 DB pointer 0x7f3c14014700
