2025/08/28-06:43:08.657721 74 RocksDB version: 8.1.1
2025/08/28-06:43:08.657732 74 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.657734 74 DB SUMMARY
2025/08/28-06:43:08.657735 74 DB Session ID:  A875X3AMNRT93RQW6NDQ
2025/08/28-06:43:08.657754 74 SST files in ./storage/collections/example_collection/1/segments/ac5ac0c0-c345-419f-86d6-bcc6c000ecec/payload_index dir, Total Num: 0, files: 
2025/08/28-06:43:08.657756 74 Write Ahead Log file in ./storage/collections/example_collection/1/segments/ac5ac0c0-c345-419f-86d6-bcc6c000ecec/payload_index: 
2025/08/28-06:43:08.657758 74                         Options.error_if_exists: 0
2025/08/28-06:43:08.657760 74                       Options.create_if_missing: 1
2025/08/28-06:43:08.657761 74                         Options.paranoid_checks: 1
2025/08/28-06:43:08.657762 74             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.657763 74                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.657764 74        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.657765 74                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.657767 74                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.657768 74                                Options.info_log: 0x7f3c2c0a6750
2025/08/28-06:43:08.657769 74                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.657771 74                              Options.statistics: (nil)
2025/08/28-06:43:08.657772 74                               Options.use_fsync: 0
2025/08/28-06:43:08.657773 74                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.657774 74                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.657775 74                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.657776 74                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.657776 74                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.657777 74                         Options.allow_fallocate: 1
2025/08/28-06:43:08.657778 74                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.657779 74                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.657780 74                        Options.use_direct_reads: 0
2025/08/28-06:43:08.657781 74                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.657782 74          Options.create_missing_column_families: 1
2025/08/28-06:43:08.657784 74                              Options.db_log_dir: 
2025/08/28-06:43:08.657785 74                                 Options.wal_dir: 
2025/08/28-06:43:08.657786 74                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.657794 74                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.657797 74                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.657798 74                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.657799 74             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.657801 74                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.657803 74                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.657804 74                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.657807 74                    Options.write_buffer_manager: 0x7f3c2c0cf8b0
2025/08/28-06:43:08.657808 74         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.657810 74           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.657811 74                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.657812 74                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.657814 74     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.657815 74                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.657816 74                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.657818 74                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.657819 74                  Options.unordered_write: 0
2025/08/28-06:43:08.657821 74         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.657822 74      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.657823 74             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.657825 74            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.657826 74                               Options.row_cache: None
2025/08/28-06:43:08.657828 74                              Options.wal_filter: None
2025/08/28-06:43:08.657829 74             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.657830 74             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.657831 74             Options.two_write_queues: 0
2025/08/28-06:43:08.657832 74             Options.manual_wal_flush: 0
2025/08/28-06:43:08.657833 74             Options.wal_compression: 0
2025/08/28-06:43:08.657834 74             Options.atomic_flush: 0
2025/08/28-06:43:08.657835 74             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.657836 74                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.657837 74                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.657838 74                 Options.log_readahead_size: 0
2025/08/28-06:43:08.657840 74                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.657841 74                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.657841 74                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.657842 74            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.657843 74             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.657844 74             Options.db_host_id: __hostname__
2025/08/28-06:43:08.657845 74             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.657846 74             Options.max_background_jobs: 2
2025/08/28-06:43:08.657847 74             Options.max_background_compactions: -1
2025/08/28-06:43:08.657848 74             Options.max_subcompactions: 1
2025/08/28-06:43:08.657849 74             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.657851 74           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.657852 74             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.657853 74             Options.max_total_wal_size: 0
2025/08/28-06:43:08.657854 74             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.657855 74                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.657857 74                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.657858 74                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.657859 74                          Options.max_open_files: 256
2025/08/28-06:43:08.657861 74                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.657862 74                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.657863 74                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.657864 74       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.657865 74                  Options.max_background_flushes: -1
2025/08/28-06:43:08.657867 74 Compression algorithms supported:
2025/08/28-06:43:08.657868 74 	kZSTD supported: 0
2025/08/28-06:43:08.657869 74 	kXpressCompression supported: 0
2025/08/28-06:43:08.657871 74 	kBZip2Compression supported: 0
2025/08/28-06:43:08.657872 74 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.657873 74 	kLZ4Compression supported: 0
2025/08/28-06:43:08.657874 74 	kZlibCompression supported: 0
2025/08/28-06:43:08.657875 74 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.657877 74 	kSnappyCompression supported: 1
2025/08/28-06:43:08.657879 74 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.657880 74 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.666419 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.666424 74           Options.merge_operator: None
2025/08/28-06:43:08.666426 74        Options.compaction_filter: None
2025/08/28-06:43:08.666427 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.666429 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.666430 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.666432 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.666462 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2c00f290)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2c081090
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.666465 74        Options.write_buffer_size: 10485760
2025/08/28-06:43:08.666467 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.666469 74          Options.compression: Snappy
2025/08/28-06:43:08.666471 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.666472 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.666474 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.666475 74             Options.num_levels: 7
2025/08/28-06:43:08.666476 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.666478 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.666479 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.666481 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.666483 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.666484 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.666485 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.666487 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.666488 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.666490 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.666491 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.666493 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.666494 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.666496 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.666497 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.666499 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.666500 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.666502 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.666503 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.666505 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.666506 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.666508 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.666509 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.666510 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.666512 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.666513 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.666515 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.666516 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.666519 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.666521 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.666522 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.666523 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.666525 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.666526 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.666527 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.666528 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.666529 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.666530 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.666532 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.666533 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.666535 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.666540 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.666542 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.666543 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.666545 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.666546 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.666548 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.666549 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.666551 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.666552 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.666554 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.666555 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.666557 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.666563 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.666564 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.666565 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.666567 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.666569 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.666571 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.666572 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.666573 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.666575 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.666576 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.666577 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.666579 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.666580 74                               Options.ttl: 2592000
2025/08/28-06:43:08.666582 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.666583 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.666584 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.666586 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.666587 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.666589 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.666590 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.666592 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.666594 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.666596 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.666597 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.666599 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.666600 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.682119 74 DB pointer 0x7f3c2c0bcc40
