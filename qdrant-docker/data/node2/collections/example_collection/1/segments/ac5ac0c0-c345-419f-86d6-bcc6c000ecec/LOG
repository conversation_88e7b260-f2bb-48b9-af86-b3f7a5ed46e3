2025/08/28-06:43:08.550273 74 RocksDB version: 8.1.1
2025/08/28-06:43:08.550472 74 Compile date 2023-04-06 16:38:52
2025/08/28-06:43:08.550480 74 DB SUMMARY
2025/08/28-06:43:08.550485 74 DB Session ID:  A875X3AMNRT93RQW6NDG
2025/08/28-06:43:08.550552 74 SST files in ./storage/collections/example_collection/1/segments/ac5ac0c0-c345-419f-86d6-bcc6c000ecec dir, Total Num: 0, files: 
2025/08/28-06:43:08.550562 74 Write Ahead Log file in ./storage/collections/example_collection/1/segments/ac5ac0c0-c345-419f-86d6-bcc6c000ecec: 
2025/08/28-06:43:08.550569 74                         Options.error_if_exists: 0
2025/08/28-06:43:08.550578 74                       Options.create_if_missing: 1
2025/08/28-06:43:08.550735 74                         Options.paranoid_checks: 1
2025/08/28-06:43:08.550772 74             Options.flush_verify_memtable_count: 1
2025/08/28-06:43:08.550779 74                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:43:08.550792 74        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:43:08.550797 74                                     Options.env: 0x561ea02739c0
2025/08/28-06:43:08.550806 74                                      Options.fs: PosixFileSystem
2025/08/28-06:43:08.550813 74                                Options.info_log: 0x7f3c2c010540
2025/08/28-06:43:08.550817 74                Options.max_file_opening_threads: 16
2025/08/28-06:43:08.550847 74                              Options.statistics: (nil)
2025/08/28-06:43:08.550876 74                               Options.use_fsync: 0
2025/08/28-06:43:08.550883 74                       Options.max_log_file_size: 1048576
2025/08/28-06:43:08.550890 74                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:43:08.550903 74                   Options.log_file_time_to_roll: 0
2025/08/28-06:43:08.550907 74                       Options.keep_log_file_num: 1000
2025/08/28-06:43:08.550915 74                    Options.recycle_log_file_num: 0
2025/08/28-06:43:08.550919 74                         Options.allow_fallocate: 1
2025/08/28-06:43:08.550936 74                        Options.allow_mmap_reads: 0
2025/08/28-06:43:08.550940 74                       Options.allow_mmap_writes: 0
2025/08/28-06:43:08.551061 74                        Options.use_direct_reads: 0
2025/08/28-06:43:08.551066 74                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:43:08.551069 74          Options.create_missing_column_families: 1
2025/08/28-06:43:08.551073 74                              Options.db_log_dir: 
2025/08/28-06:43:08.551077 74                                 Options.wal_dir: 
2025/08/28-06:43:08.551080 74                Options.table_cache_numshardbits: 6
2025/08/28-06:43:08.551084 74                         Options.WAL_ttl_seconds: 0
2025/08/28-06:43:08.551087 74                       Options.WAL_size_limit_MB: 0
2025/08/28-06:43:08.551091 74                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:43:08.551602 74             Options.manifest_preallocation_size: 4194304
2025/08/28-06:43:08.551608 74                     Options.is_fd_close_on_exec: 1
2025/08/28-06:43:08.551612 74                   Options.advise_random_on_open: 1
2025/08/28-06:43:08.551616 74                    Options.db_write_buffer_size: 0
2025/08/28-06:43:08.551620 74                    Options.write_buffer_manager: 0x7f3c2c00fae0
2025/08/28-06:43:08.551624 74         Options.access_hint_on_compaction_start: 1
2025/08/28-06:43:08.551628 74           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:43:08.551633 74                      Options.use_adaptive_mutex: 0
2025/08/28-06:43:08.551637 74                            Options.rate_limiter: (nil)
2025/08/28-06:43:08.551641 74     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:43:08.551645 74                       Options.wal_recovery_mode: 2
2025/08/28-06:43:08.551649 74                  Options.enable_thread_tracking: 0
2025/08/28-06:43:08.551652 74                  Options.enable_pipelined_write: 0
2025/08/28-06:43:08.551656 74                  Options.unordered_write: 0
2025/08/28-06:43:08.551682 74         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:43:08.551687 74      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:43:08.551691 74             Options.write_thread_max_yield_usec: 100
2025/08/28-06:43:08.551695 74            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:43:08.551699 74                               Options.row_cache: None
2025/08/28-06:43:08.551752 74                              Options.wal_filter: None
2025/08/28-06:43:08.551756 74             Options.avoid_flush_during_recovery: 0
2025/08/28-06:43:08.551760 74             Options.allow_ingest_behind: 0
2025/08/28-06:43:08.551763 74             Options.two_write_queues: 0
2025/08/28-06:43:08.551767 74             Options.manual_wal_flush: 0
2025/08/28-06:43:08.551770 74             Options.wal_compression: 0
2025/08/28-06:43:08.551773 74             Options.atomic_flush: 0
2025/08/28-06:43:08.551777 74             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:43:08.551781 74                 Options.persist_stats_to_disk: 0
2025/08/28-06:43:08.552502 74                 Options.write_dbid_to_manifest: 0
2025/08/28-06:43:08.552524 74                 Options.log_readahead_size: 0
2025/08/28-06:43:08.552529 74                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:43:08.552533 74                 Options.best_efforts_recovery: 0
2025/08/28-06:43:08.552537 74                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:43:08.552541 74            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:43:08.552544 74             Options.allow_data_in_errors: 0
2025/08/28-06:43:08.552548 74             Options.db_host_id: __hostname__
2025/08/28-06:43:08.552552 74             Options.enforce_single_del_contracts: true
2025/08/28-06:43:08.552556 74             Options.max_background_jobs: 2
2025/08/28-06:43:08.552560 74             Options.max_background_compactions: -1
2025/08/28-06:43:08.552563 74             Options.max_subcompactions: 1
2025/08/28-06:43:08.552567 74             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:43:08.552571 74           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:43:08.552575 74             Options.delayed_write_rate : 16777216
2025/08/28-06:43:08.552579 74             Options.max_total_wal_size: 0
2025/08/28-06:43:08.552582 74             Options.delete_obsolete_files_period_micros: 1800000000
2025/08/28-06:43:08.552586 74                   Options.stats_dump_period_sec: 600
2025/08/28-06:43:08.552590 74                 Options.stats_persist_period_sec: 600
2025/08/28-06:43:08.552594 74                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:43:08.552598 74                          Options.max_open_files: 256
2025/08/28-06:43:08.552602 74                          Options.bytes_per_sync: 0
2025/08/28-06:43:08.552606 74                      Options.wal_bytes_per_sync: 0
2025/08/28-06:43:08.552609 74                   Options.strict_bytes_per_sync: 0
2025/08/28-06:43:08.552613 74       Options.compaction_readahead_size: 0
2025/08/28-06:43:08.552617 74                  Options.max_background_flushes: -1
2025/08/28-06:43:08.552621 74 Compression algorithms supported:
2025/08/28-06:43:08.552626 74 	kZSTD supported: 0
2025/08/28-06:43:08.552630 74 	kXpressCompression supported: 0
2025/08/28-06:43:08.552634 74 	kBZip2Compression supported: 0
2025/08/28-06:43:08.552639 74 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:43:08.552643 74 	kLZ4Compression supported: 0
2025/08/28-06:43:08.552658 74 	kZlibCompression supported: 0
2025/08/28-06:43:08.552664 74 	kLZ4HCCompression supported: 0
2025/08/28-06:43:08.552668 74 	kSnappyCompression supported: 1
2025/08/28-06:43:08.552676 74 Fast CRC32 supported: Not supported on x86
2025/08/28-06:43:08.552680 74 DMutex implementation: pthread_mutex_t
2025/08/28-06:43:08.571393 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.571406 74           Options.merge_operator: None
2025/08/28-06:43:08.571411 74        Options.compaction_filter: None
2025/08/28-06:43:08.571421 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.571425 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.571429 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.571433 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.571510 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2c00d200)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2c00d530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.571524 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.571533 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.571537 74          Options.compression: Snappy
2025/08/28-06:43:08.571541 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.571552 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.571558 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.571561 74             Options.num_levels: 7
2025/08/28-06:43:08.571564 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.571567 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.571576 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.571579 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.571584 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.571588 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.571591 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571594 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571605 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571614 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.571618 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571636 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571639 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.571646 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.571654 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.571806 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.571811 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.571815 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.571821 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.571824 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.571830 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.571832 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.571835 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.571850 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.571854 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.571865 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.571872 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.571877 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.571884 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.571895 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.571899 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.571902 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.571905 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.571907 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.571910 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.571918 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.571920 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.571932 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.571940 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.571943 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.571950 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.571953 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.571958 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.571964 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.571971 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.571975 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.571977 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.571980 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.571986 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.571990 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.572027 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.572032 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.572035 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.572042 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.572045 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.572048 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.572069 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.572075 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.572078 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.572081 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.572086 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.572089 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.572092 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.572097 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.572100 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.572103 74                               Options.ttl: 2592000
2025/08/28-06:43:08.572111 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.572115 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.572121 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.572125 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.572128 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.572141 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.572161 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.572164 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.572168 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.572172 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.572176 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.572181 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.572184 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.588922 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.588933 74           Options.merge_operator: None
2025/08/28-06:43:08.588937 74        Options.compaction_filter: None
2025/08/28-06:43:08.588941 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.588945 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.588949 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.588953 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.589024 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2c003890)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2c003bc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.589032 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.589036 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.589041 74          Options.compression: Snappy
2025/08/28-06:43:08.589044 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.589048 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.589052 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.589055 74             Options.num_levels: 7
2025/08/28-06:43:08.589059 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.589063 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.589067 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.589071 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.589075 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.589079 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.589082 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.589085 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.589090 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.589093 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.589097 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.589101 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.589104 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.589108 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.589112 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.589116 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.589119 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.589123 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.589127 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.589131 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.589134 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.589138 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.589141 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.589146 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.589216 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.589223 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.589227 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.589230 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.589237 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.589242 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.589245 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.589248 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.589251 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.589253 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.589255 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.589258 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.589270 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.589274 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.589277 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.589280 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.589283 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.589285 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.589288 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.589293 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.589297 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.589300 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.589303 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.589306 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.589308 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.589311 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.589315 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.589318 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.589320 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.589332 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.589335 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.589338 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.589342 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.589346 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.589349 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.589351 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.589354 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.589356 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.589359 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.589362 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.589364 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.589372 74                               Options.ttl: 2592000
2025/08/28-06:43:08.589376 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.589379 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.589381 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.589384 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.589386 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.589389 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.589393 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.589395 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.589399 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.589403 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.589406 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.589409 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.589412 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.601940 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.601957 74           Options.merge_operator: None
2025/08/28-06:43:08.601962 74        Options.compaction_filter: None
2025/08/28-06:43:08.601965 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.601968 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.601972 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.601975 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.602040 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2c005ea0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2c0061d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.602050 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.602054 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.602058 74          Options.compression: Snappy
2025/08/28-06:43:08.602061 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.602065 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.602068 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.602072 74             Options.num_levels: 7
2025/08/28-06:43:08.602076 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.602079 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.602082 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.602086 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.602508 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.602511 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.602514 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.602517 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.602520 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602523 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.602526 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602530 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602533 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.602536 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.602539 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.602542 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.602545 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.602548 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.602551 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.602554 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.602556 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.602560 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.602563 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.602565 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.602568 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.602571 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.602574 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.602577 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.602581 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.602585 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.602588 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.602591 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.602594 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.602597 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.602599 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.602602 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.602605 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.602608 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.602610 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.602613 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.602616 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.602619 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.602622 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.602626 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.602629 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.602632 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.602635 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.602637 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.602640 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.602643 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.602646 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.602649 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.602653 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.602658 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.602661 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.602663 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.602667 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.602670 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.602672 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.602676 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.602678 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.602681 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.602684 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.602686 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.602689 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.602692 74                               Options.ttl: 2592000
2025/08/28-06:43:08.602694 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.602697 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.602715 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.602719 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.602722 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.602724 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.602727 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.602731 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.602734 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.602738 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.602741 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.602743 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.602747 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.619073 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.619083 74           Options.merge_operator: None
2025/08/28-06:43:08.619087 74        Options.compaction_filter: None
2025/08/28-06:43:08.619091 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.619095 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.619099 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.619102 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.619160 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2c008500)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2c008830
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.619166 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.619170 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.619174 74          Options.compression: Snappy
2025/08/28-06:43:08.619178 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.619182 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.619185 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.619189 74             Options.num_levels: 7
2025/08/28-06:43:08.619196 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.619200 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.619204 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.619208 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.619212 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.619216 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.619219 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619224 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619228 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619232 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.619236 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619240 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619243 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.619247 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.619252 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.619255 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.619259 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.619263 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.619267 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.619271 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.619275 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.619278 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.619282 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.619286 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.619290 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.619293 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.619297 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.619301 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.619307 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.619312 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.619316 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.619320 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.619324 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.619327 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.619331 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.619334 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.619338 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.619342 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.619346 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.619350 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.619353 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.619357 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.619361 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.619366 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.619370 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.619373 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.619380 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.619384 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.619388 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.619391 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.619395 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.619400 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.619405 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.619414 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.619418 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.619422 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.619426 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.619430 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.619434 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.619438 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.619442 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.619445 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.619449 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.619452 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.619456 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.619459 74                               Options.ttl: 2592000
2025/08/28-06:43:08.619463 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.619467 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.619471 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.619474 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.619478 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.619481 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.619485 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.619489 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.619494 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.619498 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.619502 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.619506 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.619510 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.637901 74               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:43:08.637907 74           Options.merge_operator: None
2025/08/28-06:43:08.637911 74        Options.compaction_filter: None
2025/08/28-06:43:08.637913 74        Options.compaction_filter_factory: None
2025/08/28-06:43:08.637916 74  Options.sst_partitioner_factory: None
2025/08/28-06:43:08.637921 74         Options.memtable_factory: SkipListFactory
2025/08/28-06:43:08.637924 74            Options.table_factory: BlockBasedTable
2025/08/28-06:43:08.637965 74            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f3c2c00ab80)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f3c2c00aeb0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:43:08.637969 74        Options.write_buffer_size: 67108864
2025/08/28-06:43:08.637972 74  Options.max_write_buffer_number: 2
2025/08/28-06:43:08.637974 74          Options.compression: Snappy
2025/08/28-06:43:08.637977 74                  Options.bottommost_compression: Disabled
2025/08/28-06:43:08.637979 74       Options.prefix_extractor: nullptr
2025/08/28-06:43:08.637982 74   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:43:08.637984 74             Options.num_levels: 7
2025/08/28-06:43:08.637987 74        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:43:08.637990 74     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:43:08.637992 74     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:43:08.637995 74            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:43:08.638010 74                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:43:08.638012 74               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:43:08.638013 74         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638014 74         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638016 74         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638017 74                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:43:08.638019 74         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638020 74         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638022 74            Options.compression_opts.window_bits: -14
2025/08/28-06:43:08.638023 74                  Options.compression_opts.level: 32767
2025/08/28-06:43:08.638025 74               Options.compression_opts.strategy: 0
2025/08/28-06:43:08.638026 74         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:43:08.638028 74         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:43:08.638029 74         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:43:08.638031 74         Options.compression_opts.parallel_threads: 1
2025/08/28-06:43:08.638032 74                  Options.compression_opts.enabled: false
2025/08/28-06:43:08.638033 74         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:43:08.638035 74      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:43:08.638036 74          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:43:08.638037 74              Options.level0_stop_writes_trigger: 36
2025/08/28-06:43:08.638039 74                   Options.target_file_size_base: 67108864
2025/08/28-06:43:08.638040 74             Options.target_file_size_multiplier: 1
2025/08/28-06:43:08.638042 74                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:43:08.638043 74 Options.level_compaction_dynamic_level_bytes: 0
2025/08/28-06:43:08.638046 74          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:43:08.638048 74 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:43:08.638049 74 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:43:08.638050 74 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:43:08.638051 74 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:43:08.638053 74 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:43:08.638054 74 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:43:08.638055 74 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:43:08.638056 74       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:43:08.638058 74                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:43:08.638059 74   Options.ignore_max_compaction_bytes_for_input: true
2025/08/28-06:43:08.638060 74                        Options.arena_block_size: 1048576
2025/08/28-06:43:08.638062 74   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:43:08.638063 74   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:43:08.638064 74                Options.disable_auto_compactions: 0
2025/08/28-06:43:08.638066 74                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:43:08.638068 74                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:43:08.638069 74 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:43:08.638070 74 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:43:08.638072 74 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:43:08.638073 74 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:43:08.638074 74 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:43:08.638075 74 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:43:08.638077 74 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:43:08.638079 74 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:43:08.638082 74                   Options.table_properties_collectors: 
2025/08/28-06:43:08.638083 74                   Options.inplace_update_support: 0
2025/08/28-06:43:08.638084 74                 Options.inplace_update_num_locks: 10000
2025/08/28-06:43:08.638086 74               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:43:08.638088 74               Options.memtable_whole_key_filtering: 0
2025/08/28-06:43:08.638089 74   Options.memtable_huge_page_size: 0
2025/08/28-06:43:08.638090 74                           Options.bloom_locality: 0
2025/08/28-06:43:08.638092 74                    Options.max_successive_merges: 0
2025/08/28-06:43:08.638093 74                Options.optimize_filters_for_hits: 0
2025/08/28-06:43:08.638094 74                Options.paranoid_file_checks: 0
2025/08/28-06:43:08.638096 74                Options.force_consistency_checks: 1
2025/08/28-06:43:08.638097 74                Options.report_bg_io_stats: 0
2025/08/28-06:43:08.638099 74                               Options.ttl: 2592000
2025/08/28-06:43:08.638100 74          Options.periodic_compaction_seconds: 0
2025/08/28-06:43:08.638102 74  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:43:08.638103 74    Options.preserve_internal_time_seconds: 0
2025/08/28-06:43:08.638104 74                       Options.enable_blob_files: false
2025/08/28-06:43:08.638106 74                           Options.min_blob_size: 0
2025/08/28-06:43:08.638107 74                          Options.blob_file_size: 268435456
2025/08/28-06:43:08.638108 74                   Options.blob_compression_type: NoCompression
2025/08/28-06:43:08.638109 74          Options.enable_blob_garbage_collection: false
2025/08/28-06:43:08.638111 74      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:43:08.638113 74 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:43:08.638115 74          Options.blob_compaction_readahead_size: 0
2025/08/28-06:43:08.638116 74                Options.blob_file_starting_level: 0
2025/08/28-06:43:08.638118 74 Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:43:08.657407 74 DB pointer 0x7f3c2c017880
