services:
  qdrant_primary:
    image: qdrant/qdrant:latest
    container_name: qdrant_primary
    volumes:
      - ./qdrant_data/primary:/qdrant/storage
    ports:
      - "6333:6333"   # HTTP API
      - "6334:6334"   # gRPC
      - "6335:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__LOG_LEVEL: "INFO"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
    command: ["./qdrant", "--uri", "http://qdrant_primary:6335"]
    restart: always
    networks:
      - qdrant_net

  qdrant_secondary:
    image: qdrant/qdrant:latest
    container_name: qdrant_secondary
    volumes:
      - ./qdrant_data/secondary:/qdrant/storage
    ports:
      - "6336:6333"   # HTTP API on different port
      - "6337:6334"   # gRPC on different port
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__LOG_LEVEL: "INFO"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
    command: ["./qdrant", "--bootstrap", "http://qdrant_primary:6335"]
    restart: always
    depends_on:
      - qdrant_primary
    networks:
      - qdrant_net

networks:
  qdrant_net:
    driver: bridge
