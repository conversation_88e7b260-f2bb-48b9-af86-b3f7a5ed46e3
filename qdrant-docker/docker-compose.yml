services:
  # Load Balancer for high availability
  nginx:
    image: nginx:alpine
    container_name: qdrant_loadbalancer
    ports:
      - "6333:6333"   # Main HTTP API endpoint
      - "6334:6334"   # Main gRPC endpoint
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - qdrant_node1
      - qdrant_node2
      - qdrant_node3
    restart: always
    networks:
      - qdrant_net

  # Primary Qdrant Node
  qdrant_node1:
    image: qdrant/qdrant:latest
    container_name: qdrant_node1
    volumes:
      - ./qdrant_data/node1:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6340:6333"   # HTTP API
      - "6341:6334"   # gRPC
      - "6342:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
      QDRANT__SERVICE__MAX_REQUEST_SIZE_MB: "1024"
      QDRANT__SERVICE__MAX_WORKERS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__UPDATE_RATE_LIMIT: "null"
      QDRANT__STORAGE__UPDATE_CONCURRENCY: "null"
      QDRANT__STORAGE__WAL__WAL_CAPACITY_MB: "256"
      QDRANT__STORAGE__WAL__WAL_SEGMENTS_AHEAD: "5"
      QDRANT__LOG_LEVEL: "INFO"
      RUN_MODE: "production"
    command: ["./qdrant", "--uri", "http://qdrant_node1:6335"]
    restart: always
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/readiness"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - qdrant_net

  # Secondary Qdrant Node
  qdrant_node2:
    image: qdrant/qdrant:latest
    container_name: qdrant_node2
    volumes:
      - ./qdrant_data/node2:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6343:6333"   # HTTP API
      - "6344:6334"   # gRPC
      - "6345:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
      QDRANT__SERVICE__MAX_REQUEST_SIZE_MB: "1024"
      QDRANT__SERVICE__MAX_WORKERS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__UPDATE_RATE_LIMIT: "null"
      QDRANT__STORAGE__UPDATE_CONCURRENCY: "null"
      QDRANT__STORAGE__WAL__WAL_CAPACITY_MB: "256"
      QDRANT__STORAGE__WAL__WAL_SEGMENTS_AHEAD: "5"
      QDRANT__LOG_LEVEL: "INFO"
      RUN_MODE: "production"
    command: ["./qdrant", "--bootstrap", "http://qdrant_node1:6335"]
    restart: always
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/readiness"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      qdrant_node1:
        condition: service_healthy
    networks:
      - qdrant_net

  # Third Qdrant Node for better distribution
  qdrant_node3:
    image: qdrant/qdrant:latest
    container_name: qdrant_node3
    volumes:
      - ./qdrant_data/node3:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6346:6333"   # HTTP API
      - "6347:6334"   # gRPC
      - "6348:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
      QDRANT__SERVICE__MAX_REQUEST_SIZE_MB: "1024"
      QDRANT__SERVICE__MAX_WORKERS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__UPDATE_RATE_LIMIT: "null"
      QDRANT__STORAGE__UPDATE_CONCURRENCY: "null"
      QDRANT__STORAGE__WAL__WAL_CAPACITY_MB: "256"
      QDRANT__STORAGE__WAL__WAL_SEGMENTS_AHEAD: "5"
      QDRANT__LOG_LEVEL: "INFO"
      RUN_MODE: "production"
    command: ["./qdrant", "--bootstrap", "http://qdrant_node1:6335"]
    restart: always
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/readiness"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      qdrant_node1:
        condition: service_healthy
    networks:
      - qdrant_net

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: qdrant_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: always
    networks:
      - qdrant_net

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: qdrant_grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: always
    networks:
      - qdrant_net

volumes:
  prometheus_data:
  grafana_data:

networks:
  qdrant_net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
