services:
  # Primary Qdrant Node
  qdrant_primary:
    image: qdrant/qdrant:latest
    container_name: qdrant_primary
    volumes:
      - ./qdrant_data/primary:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6333:6333"   # HTTP API
      - "6334:6334"   # gRPC
      - "6335:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml","--url", "http://qdrant_primary:6335"]
    restart: always
    networks:
      - qdrant_net

  # Secondary Qdrant Node 1
  qdrant_secondary1:
    image: qdrant/qdrant:latest
    container_name: qdrant_secondary1
    volumes:
      - ./qdrant_data/secondary1:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6336:6333"   # HTTP API
      - "6337:6334"   # gRPC
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml", "--bootstrap", "http://qdrant_primary:6335"]
    restart: always
    depends_on:
      - qdrant_primary
    networks:
      - qdrant_net

  # Secondary Qdrant Node 2
  qdrant_secondary2:
    image: qdrant/qdrant:latest
    container_name: qdrant_secondary2
    volumes:
      - ./qdrant_data/secondary2:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6339:6333"   # HTTP API
      - "6340:6334"   # gRPC
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml", "--bootstrap", "http://qdrant_primary:6335"]
    restart: always
    depends_on:
      - qdrant_primary
    networks:
      - qdrant_net

networks:
  qdrant_net:
    driver: bridge
