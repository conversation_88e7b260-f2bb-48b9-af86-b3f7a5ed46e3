services:
  # Primary Qdrant Node
  qdrant_primary:
    image: qdrant/qdrant:latest
    container_name: qdrant_primary
    volumes:
      - ./qdrant_data/primary:/qdrant/storage
    ports:
      - "6333:6333"   # HTTP API
      - "6334:6334"   # gRPC
      - "6335:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
      QDRANT__SERVICE__MAX_REQUEST_SIZE_MB: "1024"
      QDRANT__SERVICE__MAX_WORKERS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS: "0"
      QDRANT__STORAGE__WAL__WAL_CAPACITY_MB: "256"
      QDRANT__STORAGE__WAL__WAL_SEGMENTS_AHEAD: "5"
      QDRANT__LOG_LEVEL: "INFO"
    command: ["./qdrant", "--uri", "http://qdrant_primary:6335"]
    restart: always
    networks:
      - qdrant_net

  # Secondary Qdrant Node 1
  qdrant_secondary1:
    image: qdrant/qdrant:latest
    container_name: qdrant_secondary1
    volumes:
      - ./qdrant_data/secondary1:/qdrant/storage
    ports:
      - "6336:6333"   # HTTP API
      - "6337:6334"   # gRPC
      - "6338:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
      QDRANT__SERVICE__MAX_REQUEST_SIZE_MB: "1024"
      QDRANT__SERVICE__MAX_WORKERS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS: "0"
      QDRANT__STORAGE__WAL__WAL_CAPACITY_MB: "256"
      QDRANT__STORAGE__WAL__WAL_SEGMENTS_AHEAD: "5"
      QDRANT__LOG_LEVEL: "INFO"
    command: ["./qdrant", "--bootstrap", "http://qdrant_primary:6335"]
    restart: always
    depends_on:
      - qdrant_primary
    networks:
      - qdrant_net

  # Secondary Qdrant Node 2
  qdrant_secondary2:
    image: qdrant/qdrant:latest
    container_name: qdrant_secondary2
    volumes:
      - ./qdrant_data/secondary2:/qdrant/storage
    ports:
      - "6339:6333"   # HTTP API
      - "6340:6334"   # gRPC
      - "6341:6335"   # Cluster communication
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      QDRANT__CLUSTER__P2P__PORT: "6335"
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
      QDRANT__SERVICE__MAX_REQUEST_SIZE_MB: "1024"
      QDRANT__SERVICE__MAX_WORKERS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS: "0"
      QDRANT__STORAGE__WAL__WAL_CAPACITY_MB: "256"
      QDRANT__STORAGE__WAL__WAL_SEGMENTS_AHEAD: "5"
      QDRANT__LOG_LEVEL: "INFO"
    command: ["./qdrant", "--bootstrap", "http://qdrant_primary:6335"]
    restart: always
    depends_on:
      - qdrant_primary
    networks:
      - qdrant_net

networks:
  qdrant_net:
    driver: bridge
