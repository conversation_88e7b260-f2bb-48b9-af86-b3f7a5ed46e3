services:
  # High-Performance Single Qdrant Node
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant_single
    volumes:
      - ./qdrant_data:/qdrant/storage
    ports:
      - "6333:6333"   # HTTP API
      - "6334:6334"   # gRPC
    environment:
      QDRANT__SERVICE__HTTP_PORT: "6333"
      QDRANT__SERVICE__GRPC_PORT: "6334"
      QDRANT__SERVICE__MAX_REQUEST_SIZE_MB: "1024"
      QDRANT__SERVICE__MAX_WORKERS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS: "0"
      QDRANT__STORAGE__PERFORMANCE__MAX_OPTIMIZATION_THREADS: "0"
      QDRANT__STORAGE__WAL__WAL_CAPACITY_MB: "256"
      QDRANT__STORAGE__WAL__WAL_SEGMENTS_AHEAD: "5"
      QDRANT__LOG_LEVEL: "INFO"
    restart: always
