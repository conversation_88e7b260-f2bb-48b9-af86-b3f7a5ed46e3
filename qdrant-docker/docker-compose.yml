version: '3.9'

services:
  qdrant_primary_node:
    image: qdrant/qdrant:latest
    container_name: qdrant_primary_node
    volumes:
      - ./data/node1:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6333:6333"
      - "6334:6334"
      - "6335:6335"
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml", "--uri", "http://qdrant_primary_node:6335"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5

  qdrant_node2:
    image: qdrant/qdrant:latest
    container_name: qdrant_node2
    volumes:
      - ./data/node2:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6336:6333"
      - "6337:6334"
    depends_on:
      - qdrant_primary_node
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml", "--bootstrap", "http://qdrant_primary_node:6335"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5

  qdrant_node3:
    image: qdrant/qdrant:latest
    container_name: qdrant_node3
    volumes:
      - ./data/node3:/qdrant/storage
      - ./config/production.yaml:/qdrant/config/production.yaml:ro
    ports:
      - "6339:6333"
      - "6340:6334"
    depends_on:
      - qdrant_primary_node
    command: ["./qdrant", "--config-path", "/qdrant/config/production.yaml", "--bootstrap", "http://qdrant_primary_node:6335"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5



volumes:
  grafana-data: {}