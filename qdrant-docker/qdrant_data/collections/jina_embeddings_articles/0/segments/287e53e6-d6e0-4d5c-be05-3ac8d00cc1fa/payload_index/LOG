2025/08/28-06:27:50.227021 67 RocksDB version: 9.9.3
2025/08/28-06:27:50.227487 67 Compile date 2024-12-05 01:25:31
2025/08/28-06:27:50.227493 67 DB SUMMARY
2025/08/28-06:27:50.227495 67 Host name (Env):  f3ee714efb21
2025/08/28-06:27:50.227496 67 DB Session ID:  DKCDXOOT7YKQE7KRNTJQ
2025/08/28-06:27:50.227513 67 SST files in ./storage/collections/jina_embeddings_articles/0/segments/287e53e6-d6e0-4d5c-be05-3ac8d00cc1fa/payload_index dir, Total Num: 0, files: 
2025/08/28-06:27:50.227515 67 Write Ahead Log file in ./storage/collections/jina_embeddings_articles/0/segments/287e53e6-d6e0-4d5c-be05-3ac8d00cc1fa/payload_index: 
2025/08/28-06:27:50.227517 67                         Options.error_if_exists: 0
2025/08/28-06:27:50.227522 67                       Options.create_if_missing: 1
2025/08/28-06:27:50.227523 67                         Options.paranoid_checks: 1
2025/08/28-06:27:50.227525 67             Options.flush_verify_memtable_count: 1
2025/08/28-06:27:50.227526 67          Options.compaction_verify_record_count: 1
2025/08/28-06:27:50.227529 67                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:27:50.227532 67        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:27:50.227533 67                                     Options.env: 0x7f5bb521f540
2025/08/28-06:27:50.227535 67                                      Options.fs: PosixFileSystem
2025/08/28-06:27:50.227537 67                                Options.info_log: 0x7f5bb2c84000
2025/08/28-06:27:50.227538 67                Options.max_file_opening_threads: 16
2025/08/28-06:27:50.227540 67                              Options.statistics: (nil)
2025/08/28-06:27:50.227541 67                               Options.use_fsync: 0
2025/08/28-06:27:50.227543 67                       Options.max_log_file_size: 1048576
2025/08/28-06:27:50.227545 67                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:27:50.227546 67                   Options.log_file_time_to_roll: 0
2025/08/28-06:27:50.227552 67                       Options.keep_log_file_num: 1
2025/08/28-06:27:50.227553 67                    Options.recycle_log_file_num: 0
2025/08/28-06:27:50.227555 67                         Options.allow_fallocate: 1
2025/08/28-06:27:50.227556 67                        Options.allow_mmap_reads: 0
2025/08/28-06:27:50.227557 67                       Options.allow_mmap_writes: 0
2025/08/28-06:27:50.227558 67                        Options.use_direct_reads: 0
2025/08/28-06:27:50.227559 67                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:27:50.227561 67          Options.create_missing_column_families: 1
2025/08/28-06:27:50.227567 67                              Options.db_log_dir: 
2025/08/28-06:27:50.227571 67                                 Options.wal_dir: 
2025/08/28-06:27:50.227573 67                Options.table_cache_numshardbits: 6
2025/08/28-06:27:50.227574 67                         Options.WAL_ttl_seconds: 0
2025/08/28-06:27:50.227575 67                       Options.WAL_size_limit_MB: 0
2025/08/28-06:27:50.227576 67                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:27:50.227577 67             Options.manifest_preallocation_size: 4194304
2025/08/28-06:27:50.227579 67                     Options.is_fd_close_on_exec: 1
2025/08/28-06:27:50.227580 67                   Options.advise_random_on_open: 1
2025/08/28-06:27:50.227581 67                    Options.db_write_buffer_size: 0
2025/08/28-06:27:50.227583 67                    Options.write_buffer_manager: 0x7f5bb2c05100
2025/08/28-06:27:50.227587 67           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:27:50.227588 67                      Options.use_adaptive_mutex: 0
2025/08/28-06:27:50.227589 67                            Options.rate_limiter: (nil)
2025/08/28-06:27:50.227590 67     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:27:50.227592 67                       Options.wal_recovery_mode: 0
2025/08/28-06:27:50.227593 67                  Options.enable_thread_tracking: 0
2025/08/28-06:27:50.227594 67                  Options.enable_pipelined_write: 0
2025/08/28-06:27:50.227595 67                  Options.unordered_write: 0
2025/08/28-06:27:50.227596 67         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:27:50.227598 67      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:27:50.227601 67             Options.write_thread_max_yield_usec: 100
2025/08/28-06:27:50.227602 67            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:27:50.227603 67                               Options.row_cache: None
2025/08/28-06:27:50.227605 67                              Options.wal_filter: None
2025/08/28-06:27:50.227606 67             Options.avoid_flush_during_recovery: 0
2025/08/28-06:27:50.227607 67             Options.allow_ingest_behind: 0
2025/08/28-06:27:50.227608 67             Options.two_write_queues: 0
2025/08/28-06:27:50.227610 67             Options.manual_wal_flush: 0
2025/08/28-06:27:50.227612 67             Options.wal_compression: 0
2025/08/28-06:27:50.227614 67             Options.background_close_inactive_wals: 0
2025/08/28-06:27:50.227615 67             Options.atomic_flush: 0
2025/08/28-06:27:50.227616 67             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:27:50.227617 67             Options.prefix_seek_opt_in_only: 0
2025/08/28-06:27:50.227620 67                 Options.persist_stats_to_disk: 0
2025/08/28-06:27:50.227621 67                 Options.write_dbid_to_manifest: 1
2025/08/28-06:27:50.227622 67                 Options.write_identity_file: 1
2025/08/28-06:27:50.227623 67                 Options.log_readahead_size: 0
2025/08/28-06:27:50.227624 67                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:27:50.227625 67                 Options.best_efforts_recovery: 0
2025/08/28-06:27:50.227626 67                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:27:50.227628 67            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:27:50.227629 67             Options.allow_data_in_errors: 0
2025/08/28-06:27:50.227630 67             Options.db_host_id: __hostname__
2025/08/28-06:27:50.227631 67             Options.enforce_single_del_contracts: true
2025/08/28-06:27:50.227632 67             Options.metadata_write_temperature: kUnknown
2025/08/28-06:27:50.227633 67             Options.wal_write_temperature: kUnknown
2025/08/28-06:27:50.227634 67             Options.max_background_jobs: 2
2025/08/28-06:27:50.227635 67             Options.max_background_compactions: -1
2025/08/28-06:27:50.227636 67             Options.max_subcompactions: 1
2025/08/28-06:27:50.227638 67             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:27:50.227639 67           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:27:50.227641 67             Options.delayed_write_rate : 16777216
2025/08/28-06:27:50.227642 67             Options.max_total_wal_size: 0
2025/08/28-06:27:50.227644 67             Options.delete_obsolete_files_period_micros: 180000000
2025/08/28-06:27:50.227647 67                   Options.stats_dump_period_sec: 600
2025/08/28-06:27:50.227648 67                 Options.stats_persist_period_sec: 600
2025/08/28-06:27:50.227649 67                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:27:50.227650 67                          Options.max_open_files: 256
2025/08/28-06:27:50.227652 67                          Options.bytes_per_sync: 0
2025/08/28-06:27:50.227653 67                      Options.wal_bytes_per_sync: 0
2025/08/28-06:27:50.227654 67                   Options.strict_bytes_per_sync: 0
2025/08/28-06:27:50.227656 67       Options.compaction_readahead_size: 2097152
2025/08/28-06:27:50.227657 67                  Options.max_background_flushes: -1
2025/08/28-06:27:50.227659 67 Options.daily_offpeak_time_utc: 
2025/08/28-06:27:50.227661 67 Compression algorithms supported:
2025/08/28-06:27:50.227662 67 	kZSTD supported: 0
2025/08/28-06:27:50.227664 67 	kXpressCompression supported: 0
2025/08/28-06:27:50.227665 67 	kBZip2Compression supported: 0
2025/08/28-06:27:50.227667 67 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:27:50.227668 67 	kLZ4Compression supported: 1
2025/08/28-06:27:50.227670 67 	kZlibCompression supported: 0
2025/08/28-06:27:50.227671 67 	kLZ4HCCompression supported: 1
2025/08/28-06:27:50.227673 67 	kSnappyCompression supported: 1
2025/08/28-06:27:50.227676 67 Fast CRC32 supported: Not supported on x86
2025/08/28-06:27:50.227678 67 DMutex implementation: pthread_mutex_t
2025/08/28-06:27:50.227684 67 Jemalloc supported: 0
2025/08/28-06:27:50.231369 67               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:27:50.231385 67           Options.merge_operator: None
2025/08/28-06:27:50.231389 67        Options.compaction_filter: None
2025/08/28-06:27:50.231390 67        Options.compaction_filter_factory: None
2025/08/28-06:27:50.231391 67  Options.sst_partitioner_factory: None
2025/08/28-06:27:50.231393 67         Options.memtable_factory: SkipListFactory
2025/08/28-06:27:50.231394 67            Options.table_factory: BlockBasedTable
2025/08/28-06:27:50.231413 67            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f5bb2c00dc0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f5bb2c15190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:27:50.231417 67        Options.write_buffer_size: 10485760
2025/08/28-06:27:50.231419 67  Options.max_write_buffer_number: 2
2025/08/28-06:27:50.231423 67          Options.compression: LZ4
2025/08/28-06:27:50.231426 67                  Options.bottommost_compression: Disabled
2025/08/28-06:27:50.231427 67       Options.prefix_extractor: nullptr
2025/08/28-06:27:50.231428 67   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:27:50.231429 67             Options.num_levels: 7
2025/08/28-06:27:50.231431 67        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:27:50.231435 67     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:27:50.231436 67     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:27:50.231437 67            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:27:50.231439 67                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:27:50.231441 67               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:27:50.231444 67         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:27:50.231445 67         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:27:50.231447 67         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:27:50.231449 67                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:27:50.231450 67         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:27:50.231451 67         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:27:50.231452 67            Options.compression_opts.window_bits: -14
2025/08/28-06:27:50.231455 67                  Options.compression_opts.level: 32767
2025/08/28-06:27:50.231456 67               Options.compression_opts.strategy: 0
2025/08/28-06:27:50.231458 67         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:27:50.231459 67         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:27:50.231461 67         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:27:50.231463 67         Options.compression_opts.parallel_threads: 1
2025/08/28-06:27:50.231465 67                  Options.compression_opts.enabled: false
2025/08/28-06:27:50.231466 67         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:27:50.231469 67      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:27:50.231471 67          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:27:50.231474 67              Options.level0_stop_writes_trigger: 36
2025/08/28-06:27:50.231475 67                   Options.target_file_size_base: 67108864
2025/08/28-06:27:50.231476 67             Options.target_file_size_multiplier: 1
2025/08/28-06:27:50.231477 67                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:27:50.231478 67 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:27:50.231480 67          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:27:50.231482 67 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:27:50.231484 67 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:27:50.231485 67 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:27:50.231486 67 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:27:50.231487 67 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:27:50.231490 67 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:27:50.231491 67 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:27:50.231494 67       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:27:50.231496 67                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:27:50.231497 67                        Options.arena_block_size: 1048576
2025/08/28-06:27:50.231498 67   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:27:50.231499 67   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:27:50.231500 67                Options.disable_auto_compactions: 0
2025/08/28-06:27:50.231502 67                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:27:50.231503 67                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:27:50.231504 67 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:27:50.231505 67 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:27:50.231506 67 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:27:50.231507 67 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:27:50.231508 67 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:27:50.231510 67 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:27:50.231511 67 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:27:50.231512 67 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:27:50.231513 67 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:27:50.231515 67                   Options.table_properties_collectors: 
2025/08/28-06:27:50.231516 67                   Options.inplace_update_support: 0
2025/08/28-06:27:50.231517 67                 Options.inplace_update_num_locks: 10000
2025/08/28-06:27:50.231519 67               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:27:50.231520 67               Options.memtable_whole_key_filtering: 0
2025/08/28-06:27:50.231521 67   Options.memtable_huge_page_size: 0
2025/08/28-06:27:50.231522 67                           Options.bloom_locality: 0
2025/08/28-06:27:50.231523 67                    Options.max_successive_merges: 0
2025/08/28-06:27:50.231524 67             Options.strict_max_successive_merges: 0
2025/08/28-06:27:50.231525 67                Options.optimize_filters_for_hits: 0
2025/08/28-06:27:50.231527 67                Options.paranoid_file_checks: 0
2025/08/28-06:27:50.231528 67                Options.force_consistency_checks: 1
2025/08/28-06:27:50.231529 67                Options.report_bg_io_stats: 0
2025/08/28-06:27:50.231530 67                               Options.ttl: 2592000
2025/08/28-06:27:50.231531 67          Options.periodic_compaction_seconds: 0
2025/08/28-06:27:50.231532 67                        Options.default_temperature: kUnknown
2025/08/28-06:27:50.231533 67  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:27:50.231534 67    Options.preserve_internal_time_seconds: 0
2025/08/28-06:27:50.231535 67                       Options.enable_blob_files: false
2025/08/28-06:27:50.231536 67                           Options.min_blob_size: 0
2025/08/28-06:27:50.231537 67                          Options.blob_file_size: 268435456
2025/08/28-06:27:50.231539 67                   Options.blob_compression_type: NoCompression
2025/08/28-06:27:50.231540 67          Options.enable_blob_garbage_collection: false
2025/08/28-06:27:50.231541 67      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:27:50.231543 67 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:27:50.231544 67          Options.blob_compaction_readahead_size: 0
2025/08/28-06:27:50.231545 67                Options.blob_file_starting_level: 0
2025/08/28-06:27:50.231546 67         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:27:50.231548 67            Options.memtable_max_range_deletions: 0
2025/08/28-06:27:50.237900 67 DB pointer 0x7f5bb2c6f000
2025/08/28-06:27:50.309770 31               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:27:50.309773 31           Options.merge_operator: None
2025/08/28-06:27:50.309774 31        Options.compaction_filter: None
2025/08/28-06:27:50.309774 31        Options.compaction_filter_factory: None
2025/08/28-06:27:50.309775 31  Options.sst_partitioner_factory: None
2025/08/28-06:27:50.309776 31         Options.memtable_factory: SkipListFactory
2025/08/28-06:27:50.309777 31            Options.table_factory: BlockBasedTable
2025/08/28-06:27:50.309789 31            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f5babc76580)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f5babc229d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:27:50.309791 31        Options.write_buffer_size: 10485760
2025/08/28-06:27:50.309791 31  Options.max_write_buffer_number: 2
2025/08/28-06:27:50.309792 31          Options.compression: LZ4
2025/08/28-06:27:50.309793 31                  Options.bottommost_compression: Disabled
2025/08/28-06:27:50.309794 31       Options.prefix_extractor: nullptr
2025/08/28-06:27:50.309795 31   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:27:50.309795 31             Options.num_levels: 7
2025/08/28-06:27:50.309796 31        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:27:50.309797 31     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:27:50.309797 31     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:27:50.309798 31            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:27:50.309799 31                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:27:50.309800 31               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:27:50.309800 31         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:27:50.309801 31         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:27:50.309802 31         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:27:50.309802 31                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:27:50.309803 31         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:27:50.309804 31         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:27:50.309804 31            Options.compression_opts.window_bits: -14
2025/08/28-06:27:50.309805 31                  Options.compression_opts.level: 32767
2025/08/28-06:27:50.309806 31               Options.compression_opts.strategy: 0
2025/08/28-06:27:50.309806 31         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:27:50.309807 31         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:27:50.309808 31         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:27:50.309808 31         Options.compression_opts.parallel_threads: 1
2025/08/28-06:27:50.309809 31                  Options.compression_opts.enabled: false
2025/08/28-06:27:50.309810 31         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:27:50.309810 31      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:27:50.309811 31          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:27:50.309812 31              Options.level0_stop_writes_trigger: 36
2025/08/28-06:27:50.309813 31                   Options.target_file_size_base: 67108864
2025/08/28-06:27:50.309813 31             Options.target_file_size_multiplier: 1
2025/08/28-06:27:50.309814 31                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:27:50.309815 31 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:27:50.309816 31          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:27:50.309817 31 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:27:50.309818 31 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:27:50.309818 31 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:27:50.309819 31 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:27:50.309819 31 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:27:50.309821 31 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:27:50.309822 31 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:27:50.309822 31       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:27:50.309823 31                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:27:50.309824 31                        Options.arena_block_size: 1048576
2025/08/28-06:27:50.309824 31   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:27:50.309825 31   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:27:50.309826 31                Options.disable_auto_compactions: 0
2025/08/28-06:27:50.309827 31                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:27:50.309828 31                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:27:50.309828 31 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:27:50.309829 31 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:27:50.309830 31 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:27:50.309830 31 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:27:50.309831 31 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:27:50.309832 31 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:27:50.309833 31 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:27:50.309833 31 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:27:50.309834 31 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:27:50.309836 31                   Options.table_properties_collectors: 
2025/08/28-06:27:50.309837 31                   Options.inplace_update_support: 0
2025/08/28-06:27:50.309837 31                 Options.inplace_update_num_locks: 10000
2025/08/28-06:27:50.309838 31               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:27:50.309839 31               Options.memtable_whole_key_filtering: 0
2025/08/28-06:27:50.309840 31   Options.memtable_huge_page_size: 0
2025/08/28-06:27:50.309841 31                           Options.bloom_locality: 0
2025/08/28-06:27:50.309841 31                    Options.max_successive_merges: 0
2025/08/28-06:27:50.309842 31             Options.strict_max_successive_merges: 0
2025/08/28-06:27:50.309843 31                Options.optimize_filters_for_hits: 0
2025/08/28-06:27:50.309843 31                Options.paranoid_file_checks: 0
2025/08/28-06:27:50.309844 31                Options.force_consistency_checks: 1
2025/08/28-06:27:50.309844 31                Options.report_bg_io_stats: 0
2025/08/28-06:27:50.309845 31                               Options.ttl: 2592000
2025/08/28-06:27:50.309846 31          Options.periodic_compaction_seconds: 0
2025/08/28-06:27:50.309847 31                        Options.default_temperature: kUnknown
2025/08/28-06:27:50.309847 31  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:27:50.309848 31    Options.preserve_internal_time_seconds: 0
2025/08/28-06:27:50.309849 31                       Options.enable_blob_files: false
2025/08/28-06:27:50.309849 31                           Options.min_blob_size: 0
2025/08/28-06:27:50.309850 31                          Options.blob_file_size: 268435456
2025/08/28-06:27:50.309851 31                   Options.blob_compression_type: NoCompression
2025/08/28-06:27:50.309851 31          Options.enable_blob_garbage_collection: false
2025/08/28-06:27:50.309852 31      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:27:50.309853 31 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:27:50.309854 31          Options.blob_compaction_readahead_size: 0
2025/08/28-06:27:50.309855 31                Options.blob_file_starting_level: 0
2025/08/28-06:27:50.309856 31         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:27:50.309857 31            Options.memtable_max_range_deletions: 0
2025/08/28-06:27:50.314626 31               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:27:50.314630 31           Options.merge_operator: None
2025/08/28-06:27:50.314630 31        Options.compaction_filter: None
2025/08/28-06:27:50.314631 31        Options.compaction_filter_factory: None
2025/08/28-06:27:50.314632 31  Options.sst_partitioner_factory: None
2025/08/28-06:27:50.314633 31         Options.memtable_factory: SkipListFactory
2025/08/28-06:27:50.314633 31            Options.table_factory: BlockBasedTable
2025/08/28-06:27:50.314645 31            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f5babc76580)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f5babc229d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:27:50.314647 31        Options.write_buffer_size: 10485760
2025/08/28-06:27:50.314648 31  Options.max_write_buffer_number: 2
2025/08/28-06:27:50.314649 31          Options.compression: LZ4
2025/08/28-06:27:50.314650 31                  Options.bottommost_compression: Disabled
2025/08/28-06:27:50.314650 31       Options.prefix_extractor: nullptr
2025/08/28-06:27:50.314651 31   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:27:50.314652 31             Options.num_levels: 7
2025/08/28-06:27:50.314652 31        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:27:50.314653 31     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:27:50.314654 31     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:27:50.314654 31            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:27:50.314655 31                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:27:50.314656 31               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:27:50.314657 31         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:27:50.314657 31         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:27:50.314658 31         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:27:50.314659 31                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:27:50.314659 31         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:27:50.314660 31         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:27:50.314661 31            Options.compression_opts.window_bits: -14
2025/08/28-06:27:50.314661 31                  Options.compression_opts.level: 32767
2025/08/28-06:27:50.314662 31               Options.compression_opts.strategy: 0
2025/08/28-06:27:50.314663 31         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:27:50.314663 31         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:27:50.314665 31         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:27:50.314665 31         Options.compression_opts.parallel_threads: 1
2025/08/28-06:27:50.314666 31                  Options.compression_opts.enabled: false
2025/08/28-06:27:50.314667 31         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:27:50.314667 31      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:27:50.314668 31          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:27:50.314669 31              Options.level0_stop_writes_trigger: 36
2025/08/28-06:27:50.314670 31                   Options.target_file_size_base: 67108864
2025/08/28-06:27:50.314670 31             Options.target_file_size_multiplier: 1
2025/08/28-06:27:50.314671 31                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:27:50.314672 31 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:27:50.314673 31          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:27:50.314674 31 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:27:50.314674 31 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:27:50.314675 31 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:27:50.314676 31 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:27:50.314676 31 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:27:50.314677 31 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:27:50.314677 31 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:27:50.314678 31       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:27:50.314679 31                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:27:50.314679 31                        Options.arena_block_size: 1048576
2025/08/28-06:27:50.314680 31   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:27:50.314681 31   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:27:50.314681 31                Options.disable_auto_compactions: 0
2025/08/28-06:27:50.314682 31                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:27:50.314683 31                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:27:50.314684 31 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:27:50.314684 31 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:27:50.314685 31 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:27:50.314686 31 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:27:50.314686 31 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:27:50.314687 31 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:27:50.314688 31 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:27:50.314688 31 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:27:50.314689 31 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:27:50.314691 31                   Options.table_properties_collectors: 
2025/08/28-06:27:50.314692 31                   Options.inplace_update_support: 0
2025/08/28-06:27:50.314692 31                 Options.inplace_update_num_locks: 10000
2025/08/28-06:27:50.314693 31               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:27:50.314694 31               Options.memtable_whole_key_filtering: 0
2025/08/28-06:27:50.314695 31   Options.memtable_huge_page_size: 0
2025/08/28-06:27:50.314695 31                           Options.bloom_locality: 0
2025/08/28-06:27:50.314696 31                    Options.max_successive_merges: 0
2025/08/28-06:27:50.314697 31             Options.strict_max_successive_merges: 0
2025/08/28-06:27:50.314697 31                Options.optimize_filters_for_hits: 0
2025/08/28-06:27:50.314698 31                Options.paranoid_file_checks: 0
2025/08/28-06:27:50.314699 31                Options.force_consistency_checks: 1
2025/08/28-06:27:50.314699 31                Options.report_bg_io_stats: 0
2025/08/28-06:27:50.314713 31                               Options.ttl: 2592000
2025/08/28-06:27:50.314714 31          Options.periodic_compaction_seconds: 0
2025/08/28-06:27:50.314715 31                        Options.default_temperature: kUnknown
2025/08/28-06:27:50.314715 31  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:27:50.314716 31    Options.preserve_internal_time_seconds: 0
2025/08/28-06:27:50.314717 31                       Options.enable_blob_files: false
2025/08/28-06:27:50.314718 31                           Options.min_blob_size: 0
2025/08/28-06:27:50.314719 31                          Options.blob_file_size: 268435456
2025/08/28-06:27:50.314720 31                   Options.blob_compression_type: NoCompression
2025/08/28-06:27:50.314721 31          Options.enable_blob_garbage_collection: false
2025/08/28-06:27:50.314723 31      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:27:50.314724 31 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:27:50.314725 31          Options.blob_compaction_readahead_size: 0
2025/08/28-06:27:50.314726 31                Options.blob_file_starting_level: 0
2025/08/28-06:27:50.314727 31         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:27:50.314729 31            Options.memtable_max_range_deletions: 0
