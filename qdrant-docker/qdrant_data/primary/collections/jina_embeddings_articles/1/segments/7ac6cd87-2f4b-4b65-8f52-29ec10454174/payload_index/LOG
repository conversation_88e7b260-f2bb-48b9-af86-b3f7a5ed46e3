2025/08/28-06:01:39.160503 670 RocksDB version: 9.9.3
2025/08/28-06:01:39.160543 670 Compile date 2024-12-05 01:25:31
2025/08/28-06:01:39.160549 670 DB SUMMARY
2025/08/28-06:01:39.160554 670 Host name (Env):  adbfaf2ea9d4
2025/08/28-06:01:39.160557 670 DB Session ID:  6DXSETNP6YHOH7T8I5X2
2025/08/28-06:01:39.160620 670 SST files in ./storage/collections/jina_embeddings_articles/1/segments/7ac6cd87-2f4b-4b65-8f52-29ec10454174/payload_index dir, Total Num: 0, files: 
2025/08/28-06:01:39.160625 670 Write Ahead Log file in ./storage/collections/jina_embeddings_articles/1/segments/7ac6cd87-2f4b-4b65-8f52-29ec10454174/payload_index: 
2025/08/28-06:01:39.160630 670                         Options.error_if_exists: 0
2025/08/28-06:01:39.160633 670                       Options.create_if_missing: 1
2025/08/28-06:01:39.160637 670                         Options.paranoid_checks: 1
2025/08/28-06:01:39.160640 670             Options.flush_verify_memtable_count: 1
2025/08/28-06:01:39.160650 670          Options.compaction_verify_record_count: 1
2025/08/28-06:01:39.160691 670                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:01:39.160694 670        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:01:39.160748 670                                     Options.env: 0x7f1f2f230460
2025/08/28-06:01:39.160754 670                                      Options.fs: PosixFileSystem
2025/08/28-06:01:39.160763 670                                Options.info_log: 0x7f1f18a7f600
2025/08/28-06:01:39.160766 670                Options.max_file_opening_threads: 16
2025/08/28-06:01:39.160772 670                              Options.statistics: (nil)
2025/08/28-06:01:39.160780 670                               Options.use_fsync: 0
2025/08/28-06:01:39.160783 670                       Options.max_log_file_size: 1048576
2025/08/28-06:01:39.160786 670                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:01:39.160802 670                   Options.log_file_time_to_roll: 0
2025/08/28-06:01:39.160805 670                       Options.keep_log_file_num: 1
2025/08/28-06:01:39.160817 670                    Options.recycle_log_file_num: 0
2025/08/28-06:01:39.160819 670                         Options.allow_fallocate: 1
2025/08/28-06:01:39.160822 670                        Options.allow_mmap_reads: 0
2025/08/28-06:01:39.160833 670                       Options.allow_mmap_writes: 0
2025/08/28-06:01:39.160865 670                        Options.use_direct_reads: 0
2025/08/28-06:01:39.160868 670                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:01:39.160871 670          Options.create_missing_column_families: 1
2025/08/28-06:01:39.160874 670                              Options.db_log_dir: 
2025/08/28-06:01:39.160886 670                                 Options.wal_dir: 
2025/08/28-06:01:39.160893 670                Options.table_cache_numshardbits: 6
2025/08/28-06:01:39.160899 670                         Options.WAL_ttl_seconds: 0
2025/08/28-06:01:39.160907 670                       Options.WAL_size_limit_MB: 0
2025/08/28-06:01:39.160916 670                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:01:39.160919 670             Options.manifest_preallocation_size: 4194304
2025/08/28-06:01:39.160922 670                     Options.is_fd_close_on_exec: 1
2025/08/28-06:01:39.160925 670                   Options.advise_random_on_open: 1
2025/08/28-06:01:39.160928 670                    Options.db_write_buffer_size: 0
2025/08/28-06:01:39.160941 670                    Options.write_buffer_manager: 0x7f1f18a01820
2025/08/28-06:01:39.160968 670           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:01:39.160971 670                      Options.use_adaptive_mutex: 0
2025/08/28-06:01:39.160976 670                            Options.rate_limiter: (nil)
2025/08/28-06:01:39.160988 670     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:01:39.160991 670                       Options.wal_recovery_mode: 0
2025/08/28-06:01:39.160994 670                  Options.enable_thread_tracking: 0
2025/08/28-06:01:39.161024 670                  Options.enable_pipelined_write: 0
2025/08/28-06:01:39.161031 670                  Options.unordered_write: 0
2025/08/28-06:01:39.161034 670         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:01:39.161037 670      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:01:39.161043 670             Options.write_thread_max_yield_usec: 100
2025/08/28-06:01:39.161046 670            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:01:39.161053 670                               Options.row_cache: None
2025/08/28-06:01:39.161056 670                              Options.wal_filter: None
2025/08/28-06:01:39.161059 670             Options.avoid_flush_during_recovery: 0
2025/08/28-06:01:39.161062 670             Options.allow_ingest_behind: 0
2025/08/28-06:01:39.161067 670             Options.two_write_queues: 0
2025/08/28-06:01:39.161070 670             Options.manual_wal_flush: 0
2025/08/28-06:01:39.161072 670             Options.wal_compression: 0
2025/08/28-06:01:39.161080 670             Options.background_close_inactive_wals: 0
2025/08/28-06:01:39.161087 670             Options.atomic_flush: 0
2025/08/28-06:01:39.161097 670             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:01:39.161100 670             Options.prefix_seek_opt_in_only: 0
2025/08/28-06:01:39.161103 670                 Options.persist_stats_to_disk: 0
2025/08/28-06:01:39.161105 670                 Options.write_dbid_to_manifest: 1
2025/08/28-06:01:39.161121 670                 Options.write_identity_file: 1
2025/08/28-06:01:39.161123 670                 Options.log_readahead_size: 0
2025/08/28-06:01:39.161126 670                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:01:39.161128 670                 Options.best_efforts_recovery: 0
2025/08/28-06:01:39.161131 670                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:01:39.161137 670            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:01:39.161140 670             Options.allow_data_in_errors: 0
2025/08/28-06:01:39.161143 670             Options.db_host_id: __hostname__
2025/08/28-06:01:39.161167 670             Options.enforce_single_del_contracts: true
2025/08/28-06:01:39.161171 670             Options.metadata_write_temperature: kUnknown
2025/08/28-06:01:39.161174 670             Options.wal_write_temperature: kUnknown
2025/08/28-06:01:39.161179 670             Options.max_background_jobs: 2
2025/08/28-06:01:39.161182 670             Options.max_background_compactions: -1
2025/08/28-06:01:39.161185 670             Options.max_subcompactions: 1
2025/08/28-06:01:39.161190 670             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:01:39.161193 670           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:01:39.161196 670             Options.delayed_write_rate : 16777216
2025/08/28-06:01:39.161203 670             Options.max_total_wal_size: 0
2025/08/28-06:01:39.161209 670             Options.delete_obsolete_files_period_micros: 180000000
2025/08/28-06:01:39.161212 670                   Options.stats_dump_period_sec: 600
2025/08/28-06:01:39.161214 670                 Options.stats_persist_period_sec: 600
2025/08/28-06:01:39.161217 670                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:01:39.161220 670                          Options.max_open_files: 256
2025/08/28-06:01:39.161223 670                          Options.bytes_per_sync: 0
2025/08/28-06:01:39.161226 670                      Options.wal_bytes_per_sync: 0
2025/08/28-06:01:39.161232 670                   Options.strict_bytes_per_sync: 0
2025/08/28-06:01:39.161237 670       Options.compaction_readahead_size: 2097152
2025/08/28-06:01:39.161242 670                  Options.max_background_flushes: -1
2025/08/28-06:01:39.161246 670 Options.daily_offpeak_time_utc: 
2025/08/28-06:01:39.161249 670 Compression algorithms supported:
2025/08/28-06:01:39.161252 670 	kZSTD supported: 0
2025/08/28-06:01:39.161255 670 	kXpressCompression supported: 0
2025/08/28-06:01:39.161258 670 	kBZip2Compression supported: 0
2025/08/28-06:01:39.161270 670 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:01:39.161273 670 	kLZ4Compression supported: 1
2025/08/28-06:01:39.161276 670 	kZlibCompression supported: 0
2025/08/28-06:01:39.161278 670 	kLZ4HCCompression supported: 1
2025/08/28-06:01:39.161337 670 	kSnappyCompression supported: 1
2025/08/28-06:01:39.161344 670 Fast CRC32 supported: Not supported on x86
2025/08/28-06:01:39.161347 670 DMutex implementation: pthread_mutex_t
2025/08/28-06:01:39.161350 670 Jemalloc supported: 0
2025/08/28-06:01:39.168818 670               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.168828 670           Options.merge_operator: None
2025/08/28-06:01:39.168832 670        Options.compaction_filter: None
2025/08/28-06:01:39.168836 670        Options.compaction_filter_factory: None
2025/08/28-06:01:39.168840 670  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.168844 670         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.168847 670            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.168919 670            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f18a00180)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f18a16550
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.168954 670        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.168963 670  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.168974 670          Options.compression: LZ4
2025/08/28-06:01:39.168978 670                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.168983 670       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.168991 670   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.168999 670             Options.num_levels: 7
2025/08/28-06:01:39.169003 670        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.169007 670     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.169021 670     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.169026 670            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.169048 670                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.169059 670               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.169066 670         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.169070 670         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.169074 670         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.169086 670                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.169090 670         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.169128 670         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.169154 670            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.169159 670                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.169280 670               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.169286 670         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.169290 670         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.169294 670         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.169298 670         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.169303 670                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.169307 670         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.169312 670      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.169315 670          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.169319 670              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.169323 670                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.169327 670             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.169333 670                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.169338 670 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.169346 670          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.169352 670 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.169356 670 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.169360 670 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.169364 670 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.169368 670 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.169373 670 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.169376 670 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.169386 670       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.169390 670                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.169394 670                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.169397 670   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.169402 670   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.169406 670                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.169412 670                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.169416 670                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.169420 670 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.169424 670 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.169429 670 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.169433 670 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.169438 670 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.169443 670 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.169447 670 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.169451 670 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.169456 670 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.169465 670                   Options.table_properties_collectors: 
2025/08/28-06:01:39.169469 670                   Options.inplace_update_support: 0
2025/08/28-06:01:39.169474 670                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.169479 670               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.169484 670               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.169488 670   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.169492 670                           Options.bloom_locality: 0
2025/08/28-06:01:39.169495 670                    Options.max_successive_merges: 0
2025/08/28-06:01:39.169507 670             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.169513 670                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.169517 670                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.169521 670                Options.force_consistency_checks: 1
2025/08/28-06:01:39.169525 670                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.169529 670                               Options.ttl: 2592000
2025/08/28-06:01:39.169533 670          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.169538 670                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.169542 670  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.169544 670    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.169547 670                       Options.enable_blob_files: false
2025/08/28-06:01:39.169549 670                           Options.min_blob_size: 0
2025/08/28-06:01:39.169551 670                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.169554 670                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.169557 670          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.169560 670      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.169564 670 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.169566 670          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.169569 670                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.169572 670         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.169575 670            Options.memtable_max_range_deletions: 0
2025/08/28-06:01:39.192731 670 DB pointer 0x7f1f18a6d800
2025/08/28-06:01:39.515226 33               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.515231 33           Options.merge_operator: None
2025/08/28-06:01:39.515233 33        Options.compaction_filter: None
2025/08/28-06:01:39.515234 33        Options.compaction_filter_factory: None
2025/08/28-06:01:39.515235 33  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.515237 33         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.515238 33            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.515258 33            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f41a00f60)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f41ab8850
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.515262 33        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.515263 33  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.515265 33          Options.compression: LZ4
2025/08/28-06:01:39.515266 33                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.515268 33       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.515269 33   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.515270 33             Options.num_levels: 7
2025/08/28-06:01:39.515271 33        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.515272 33     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.515274 33     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.515275 33            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.515276 33                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.515277 33               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.515278 33         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.515280 33         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.515281 33         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.515282 33                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.515283 33         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.515284 33         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.515285 33            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.515287 33                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.515288 33               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.515289 33         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.515290 33         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.515292 33         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.515294 33         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.515295 33                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.515296 33         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.515297 33      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.515299 33          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.515300 33              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.515301 33                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.515302 33             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.515303 33                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.515304 33 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.515307 33          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.515308 33 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.515309 33 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.515311 33 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.515312 33 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.515313 33 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.515314 33 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.515315 33 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.515316 33       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.515317 33                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.515318 33                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.515319 33   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.515320 33   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.515322 33                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.515324 33                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.515325 33                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.515326 33 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.515327 33 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.515328 33 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.515330 33 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.515331 33 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.515332 33 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.515333 33 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.515334 33 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.515335 33 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.515339 33                   Options.table_properties_collectors: 
2025/08/28-06:01:39.515341 33                   Options.inplace_update_support: 0
2025/08/28-06:01:39.515342 33                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.515343 33               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.515345 33               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.515346 33   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.515347 33                           Options.bloom_locality: 0
2025/08/28-06:01:39.515348 33                    Options.max_successive_merges: 0
2025/08/28-06:01:39.515349 33             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.515350 33                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.515352 33                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.515353 33                Options.force_consistency_checks: 1
2025/08/28-06:01:39.515356 33                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.515357 33                               Options.ttl: 2592000
2025/08/28-06:01:39.515358 33          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.515359 33                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.515360 33  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.515362 33    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.515363 33                       Options.enable_blob_files: false
2025/08/28-06:01:39.515364 33                           Options.min_blob_size: 0
2025/08/28-06:01:39.515365 33                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.515366 33                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.515367 33          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.515369 33      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.515370 33 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.515372 33          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.515373 33                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.515374 33         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.515376 33            Options.memtable_max_range_deletions: 0
