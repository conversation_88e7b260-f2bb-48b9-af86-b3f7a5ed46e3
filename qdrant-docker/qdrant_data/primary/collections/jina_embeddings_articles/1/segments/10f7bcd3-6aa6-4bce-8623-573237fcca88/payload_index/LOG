2025/08/28-06:01:39.160375 667 RocksDB version: 9.9.3
2025/08/28-06:01:39.160499 667 Compile date 2024-12-05 01:25:31
2025/08/28-06:01:39.160504 667 DB SUMMARY
2025/08/28-06:01:39.160510 667 Host name (Env):  adbfaf2ea9d4
2025/08/28-06:01:39.160513 667 DB Session ID:  6DXSETNP6YHOH7T8I5WY
2025/08/28-06:01:39.160548 667 SST files in ./storage/collections/jina_embeddings_articles/1/segments/10f7bcd3-6aa6-4bce-8623-573237fcca88/payload_index dir, Total Num: 0, files: 
2025/08/28-06:01:39.160551 667 Write Ahead Log file in ./storage/collections/jina_embeddings_articles/1/segments/10f7bcd3-6aa6-4bce-8623-573237fcca88/payload_index: 
2025/08/28-06:01:39.160554 667                         Options.error_if_exists: 0
2025/08/28-06:01:39.160557 667                       Options.create_if_missing: 1
2025/08/28-06:01:39.160578 667                         Options.paranoid_checks: 1
2025/08/28-06:01:39.160581 667             Options.flush_verify_memtable_count: 1
2025/08/28-06:01:39.160584 667          Options.compaction_verify_record_count: 1
2025/08/28-06:01:39.160586 667                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:01:39.160588 667        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:01:39.160591 667                                     Options.env: 0x7f1f2f230460
2025/08/28-06:01:39.160595 667                                      Options.fs: PosixFileSystem
2025/08/28-06:01:39.160597 667                                Options.info_log: 0x7f1f31a82000
2025/08/28-06:01:39.160599 667                Options.max_file_opening_threads: 16
2025/08/28-06:01:39.160606 667                              Options.statistics: (nil)
2025/08/28-06:01:39.160608 667                               Options.use_fsync: 0
2025/08/28-06:01:39.160613 667                       Options.max_log_file_size: 1048576
2025/08/28-06:01:39.160628 667                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:01:39.160630 667                   Options.log_file_time_to_roll: 0
2025/08/28-06:01:39.160635 667                       Options.keep_log_file_num: 1
2025/08/28-06:01:39.160639 667                    Options.recycle_log_file_num: 0
2025/08/28-06:01:39.160648 667                         Options.allow_fallocate: 1
2025/08/28-06:01:39.160664 667                        Options.allow_mmap_reads: 0
2025/08/28-06:01:39.160666 667                       Options.allow_mmap_writes: 0
2025/08/28-06:01:39.160668 667                        Options.use_direct_reads: 0
2025/08/28-06:01:39.160671 667                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:01:39.160676 667          Options.create_missing_column_families: 1
2025/08/28-06:01:39.160678 667                              Options.db_log_dir: 
2025/08/28-06:01:39.160681 667                                 Options.wal_dir: 
2025/08/28-06:01:39.160686 667                Options.table_cache_numshardbits: 6
2025/08/28-06:01:39.160688 667                         Options.WAL_ttl_seconds: 0
2025/08/28-06:01:39.160692 667                       Options.WAL_size_limit_MB: 0
2025/08/28-06:01:39.160695 667                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:01:39.160724 667             Options.manifest_preallocation_size: 4194304
2025/08/28-06:01:39.160727 667                     Options.is_fd_close_on_exec: 1
2025/08/28-06:01:39.160737 667                   Options.advise_random_on_open: 1
2025/08/28-06:01:39.160744 667                    Options.db_write_buffer_size: 0
2025/08/28-06:01:39.160748 667                    Options.write_buffer_manager: 0x7f1f31a01280
2025/08/28-06:01:39.160751 667           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:01:39.160755 667                      Options.use_adaptive_mutex: 0
2025/08/28-06:01:39.160763 667                            Options.rate_limiter: (nil)
2025/08/28-06:01:39.160769 667     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:01:39.160771 667                       Options.wal_recovery_mode: 0
2025/08/28-06:01:39.160773 667                  Options.enable_thread_tracking: 0
2025/08/28-06:01:39.160780 667                  Options.enable_pipelined_write: 0
2025/08/28-06:01:39.160786 667                  Options.unordered_write: 0
2025/08/28-06:01:39.160789 667         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:01:39.160823 667      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:01:39.160826 667             Options.write_thread_max_yield_usec: 100
2025/08/28-06:01:39.160829 667            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:01:39.160834 667                               Options.row_cache: None
2025/08/28-06:01:39.160846 667                              Options.wal_filter: None
2025/08/28-06:01:39.160854 667             Options.avoid_flush_during_recovery: 0
2025/08/28-06:01:39.160874 667             Options.allow_ingest_behind: 0
2025/08/28-06:01:39.160884 667             Options.two_write_queues: 0
2025/08/28-06:01:39.160888 667             Options.manual_wal_flush: 0
2025/08/28-06:01:39.160891 667             Options.wal_compression: 0
2025/08/28-06:01:39.160896 667             Options.background_close_inactive_wals: 0
2025/08/28-06:01:39.160901 667             Options.atomic_flush: 0
2025/08/28-06:01:39.160903 667             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:01:39.160905 667             Options.prefix_seek_opt_in_only: 0
2025/08/28-06:01:39.160907 667                 Options.persist_stats_to_disk: 0
2025/08/28-06:01:39.160923 667                 Options.write_dbid_to_manifest: 1
2025/08/28-06:01:39.160926 667                 Options.write_identity_file: 1
2025/08/28-06:01:39.160928 667                 Options.log_readahead_size: 0
2025/08/28-06:01:39.160933 667                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:01:39.160939 667                 Options.best_efforts_recovery: 0
2025/08/28-06:01:39.160941 667                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:01:39.160955 667            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:01:39.160963 667             Options.allow_data_in_errors: 0
2025/08/28-06:01:39.160966 667             Options.db_host_id: __hostname__
2025/08/28-06:01:39.160970 667             Options.enforce_single_del_contracts: true
2025/08/28-06:01:39.160973 667             Options.metadata_write_temperature: kUnknown
2025/08/28-06:01:39.160975 667             Options.wal_write_temperature: kUnknown
2025/08/28-06:01:39.160978 667             Options.max_background_jobs: 2
2025/08/28-06:01:39.160980 667             Options.max_background_compactions: -1
2025/08/28-06:01:39.160982 667             Options.max_subcompactions: 1
2025/08/28-06:01:39.160984 667             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:01:39.160986 667           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:01:39.160989 667             Options.delayed_write_rate : 16777216
2025/08/28-06:01:39.160991 667             Options.max_total_wal_size: 0
2025/08/28-06:01:39.160993 667             Options.delete_obsolete_files_period_micros: 180000000
2025/08/28-06:01:39.160996 667                   Options.stats_dump_period_sec: 600
2025/08/28-06:01:39.160999 667                 Options.stats_persist_period_sec: 600
2025/08/28-06:01:39.161002 667                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:01:39.161005 667                          Options.max_open_files: 256
2025/08/28-06:01:39.161008 667                          Options.bytes_per_sync: 0
2025/08/28-06:01:39.161011 667                      Options.wal_bytes_per_sync: 0
2025/08/28-06:01:39.161013 667                   Options.strict_bytes_per_sync: 0
2025/08/28-06:01:39.161017 667       Options.compaction_readahead_size: 2097152
2025/08/28-06:01:39.161020 667                  Options.max_background_flushes: -1
2025/08/28-06:01:39.161023 667 Options.daily_offpeak_time_utc: 
2025/08/28-06:01:39.161026 667 Compression algorithms supported:
2025/08/28-06:01:39.161030 667 	kZSTD supported: 0
2025/08/28-06:01:39.161060 667 	kXpressCompression supported: 0
2025/08/28-06:01:39.161069 667 	kBZip2Compression supported: 0
2025/08/28-06:01:39.161075 667 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:01:39.161096 667 	kLZ4Compression supported: 1
2025/08/28-06:01:39.161098 667 	kZlibCompression supported: 0
2025/08/28-06:01:39.161100 667 	kLZ4HCCompression supported: 1
2025/08/28-06:01:39.161103 667 	kSnappyCompression supported: 1
2025/08/28-06:01:39.161117 667 Fast CRC32 supported: Not supported on x86
2025/08/28-06:01:39.161124 667 DMutex implementation: pthread_mutex_t
2025/08/28-06:01:39.161127 667 Jemalloc supported: 0
2025/08/28-06:01:39.168647 667               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.168661 667           Options.merge_operator: None
2025/08/28-06:01:39.168666 667        Options.compaction_filter: None
2025/08/28-06:01:39.168674 667        Options.compaction_filter_factory: None
2025/08/28-06:01:39.168682 667  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.168691 667         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.168739 667            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.168867 667            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f31a00140)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f31a11190
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.168880 667        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.168884 667  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.168933 667          Options.compression: LZ4
2025/08/28-06:01:39.168939 667                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.168943 667       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.168946 667   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.168950 667             Options.num_levels: 7
2025/08/28-06:01:39.168954 667        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.168958 667     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.168961 667     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.168964 667            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.168970 667                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.168974 667               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.168978 667         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.168981 667         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.168984 667         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.168988 667                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.168991 667         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.169004 667         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.169008 667            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.169012 667                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.169030 667               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.169047 667         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.169090 667         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.169107 667         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.169110 667         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.169114 667                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.169118 667         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.169123 667      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.169126 667          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.169132 667              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.169135 667                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.169139 667             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.169151 667                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.169154 667 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.169159 667          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.169169 667 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.169173 667 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.169177 667 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.169179 667 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.169182 667 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.169186 667 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.169189 667 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.169202 667       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.169206 667                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.169209 667                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.169213 667   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.169216 667   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.169220 667                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.169224 667                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.169228 667                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.169232 667 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.169236 667 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.169240 667 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.169243 667 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.169247 667 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.169252 667 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.169255 667 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.169259 667 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.169262 667 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.169272 667                   Options.table_properties_collectors: 
2025/08/28-06:01:39.169275 667                   Options.inplace_update_support: 0
2025/08/28-06:01:39.169279 667                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.169284 667               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.169289 667               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.169297 667   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.169301 667                           Options.bloom_locality: 0
2025/08/28-06:01:39.169305 667                    Options.max_successive_merges: 0
2025/08/28-06:01:39.169315 667             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.169319 667                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.169323 667                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.169327 667                Options.force_consistency_checks: 1
2025/08/28-06:01:39.169333 667                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.169336 667                               Options.ttl: 2592000
2025/08/28-06:01:39.169340 667          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.169344 667                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.169347 667  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.169351 667    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.169354 667                       Options.enable_blob_files: false
2025/08/28-06:01:39.169358 667                           Options.min_blob_size: 0
2025/08/28-06:01:39.169362 667                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.169365 667                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.169369 667          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.169374 667      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.169379 667 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.169383 667          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.169387 667                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.169391 667         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.169396 667            Options.memtable_max_range_deletions: 0
2025/08/28-06:01:39.196024 667 DB pointer 0x7f1f31a6d000
2025/08/28-06:01:39.469567 33               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.469582 33           Options.merge_operator: None
2025/08/28-06:01:39.469586 33        Options.compaction_filter: None
2025/08/28-06:01:39.469588 33        Options.compaction_filter_factory: None
2025/08/28-06:01:39.469590 33  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.469593 33         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.469596 33            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.469740 33            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f18a00c20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f41ab8b50
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.469764 33        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.469768 33  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.469771 33          Options.compression: LZ4
2025/08/28-06:01:39.469774 33                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.469776 33       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.469778 33   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.469780 33             Options.num_levels: 7
2025/08/28-06:01:39.469782 33        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.469784 33     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.469786 33     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.469789 33            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.469831 33                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.469857 33               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.469864 33         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.469866 33         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.469869 33         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.469871 33                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.469873 33         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.469875 33         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.469877 33            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.469880 33                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.469882 33               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.469884 33         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.469886 33         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.469888 33         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.469890 33         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.469892 33                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.469894 33         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.469896 33      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.469898 33          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.469900 33              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.469903 33                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.469905 33             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.469907 33                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.469909 33 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.469913 33          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.469917 33 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.469919 33 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.469922 33 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.469924 33 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.469926 33 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.469927 33 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.469929 33 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.469931 33       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.469933 33                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.469935 33                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.469937 33   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.469939 33   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.469941 33                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.469946 33                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.469948 33                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.469950 33 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.469952 33 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.469954 33 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.469957 33 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.469959 33 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.469962 33 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.469963 33 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.469966 33 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.469968 33 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.469974 33                   Options.table_properties_collectors: 
2025/08/28-06:01:39.469977 33                   Options.inplace_update_support: 0
2025/08/28-06:01:39.469979 33                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.469982 33               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.469984 33               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.469986 33   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.469989 33                           Options.bloom_locality: 0
2025/08/28-06:01:39.469990 33                    Options.max_successive_merges: 0
2025/08/28-06:01:39.469992 33             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.469994 33                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.469996 33                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.469998 33                Options.force_consistency_checks: 1
2025/08/28-06:01:39.470000 33                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.470002 33                               Options.ttl: 2592000
2025/08/28-06:01:39.470005 33          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.470007 33                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.470009 33  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.470011 33    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.470013 33                       Options.enable_blob_files: false
2025/08/28-06:01:39.470015 33                           Options.min_blob_size: 0
2025/08/28-06:01:39.470017 33                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.470019 33                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.470021 33          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.470024 33      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.470027 33 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.470030 33          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.470032 33                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.470034 33         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.470036 33            Options.memtable_max_range_deletions: 0
2025/08/28-06:01:39.482244 33               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.482255 33           Options.merge_operator: None
2025/08/28-06:01:39.482258 33        Options.compaction_filter: None
2025/08/28-06:01:39.482260 33        Options.compaction_filter_factory: None
2025/08/28-06:01:39.482262 33  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.482265 33         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.482267 33            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.482310 33            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f18a00c20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f41ab8b50
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.482317 33        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.482320 33  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.482323 33          Options.compression: LZ4
2025/08/28-06:01:39.482326 33                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.482328 33       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.482330 33   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.482332 33             Options.num_levels: 7
2025/08/28-06:01:39.482335 33        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.482337 33     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.482340 33     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.482342 33            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.482345 33                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.482347 33               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.482350 33         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.482352 33         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.482355 33         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.482357 33                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.482359 33         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.482362 33         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.482364 33            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.482367 33                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.482369 33               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.482371 33         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.482373 33         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.482376 33         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.482378 33         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.482380 33                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.482382 33         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.482385 33      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.482387 33          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.482389 33              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.482392 33                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.482394 33             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.482396 33                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.482399 33 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.482403 33          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.482406 33 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.482408 33 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.482411 33 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.482413 33 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.482415 33 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.482417 33 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.482419 33 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.482422 33       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.482424 33                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.482426 33                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.482429 33   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.482431 33   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.482433 33                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.482438 33                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.482441 33                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.482443 33 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.482445 33 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.482448 33 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.482450 33 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.482453 33 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.482456 33 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.482458 33 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.482461 33 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.482463 33 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.482471 33                   Options.table_properties_collectors: 
2025/08/28-06:01:39.482474 33                   Options.inplace_update_support: 0
2025/08/28-06:01:39.482476 33                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.482479 33               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.482482 33               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.482484 33   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.482487 33                           Options.bloom_locality: 0
2025/08/28-06:01:39.482489 33                    Options.max_successive_merges: 0
2025/08/28-06:01:39.482492 33             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.482494 33                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.482496 33                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.482498 33                Options.force_consistency_checks: 1
2025/08/28-06:01:39.482501 33                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.482503 33                               Options.ttl: 2592000
2025/08/28-06:01:39.482506 33          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.482508 33                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.482510 33  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.482513 33    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.482515 33                       Options.enable_blob_files: false
2025/08/28-06:01:39.482517 33                           Options.min_blob_size: 0
2025/08/28-06:01:39.482520 33                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.482523 33                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.482525 33          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.482528 33      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.482531 33 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.482534 33          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.482536 33                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.482538 33         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.482541 33            Options.memtable_max_range_deletions: 0
