2025/08/28-06:01:39.160397 668 RocksDB version: 9.9.3
2025/08/28-06:01:39.160514 668 Compile date 2024-12-05 01:25:31
2025/08/28-06:01:39.160517 668 DB SUMMARY
2025/08/28-06:01:39.160522 668 Host name (Env):  adbfaf2ea9d4
2025/08/28-06:01:39.160524 668 DB Session ID:  6DXSETNP6YHOH7T8I5WW
2025/08/28-06:01:39.160568 668 SST files in ./storage/collections/jina_embeddings_articles/1/segments/0662664b-18b2-4f79-a215-ed6ca45cfe3c/payload_index dir, Total Num: 0, files: 
2025/08/28-06:01:39.160578 668 Write Ahead Log file in ./storage/collections/jina_embeddings_articles/1/segments/0662664b-18b2-4f79-a215-ed6ca45cfe3c/payload_index: 
2025/08/28-06:01:39.160584 668                         Options.error_if_exists: 0
2025/08/28-06:01:39.160587 668                       Options.create_if_missing: 1
2025/08/28-06:01:39.160589 668                         Options.paranoid_checks: 1
2025/08/28-06:01:39.160591 668             Options.flush_verify_memtable_count: 1
2025/08/28-06:01:39.160593 668          Options.compaction_verify_record_count: 1
2025/08/28-06:01:39.160597 668                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:01:39.160600 668        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:01:39.160606 668                                     Options.env: 0x7f1f2f230460
2025/08/28-06:01:39.160609 668                                      Options.fs: PosixFileSystem
2025/08/28-06:01:39.160611 668                                Options.info_log: 0x7f1f2f693000
2025/08/28-06:01:39.160614 668                Options.max_file_opening_threads: 16
2025/08/28-06:01:39.160621 668                              Options.statistics: (nil)
2025/08/28-06:01:39.160624 668                               Options.use_fsync: 0
2025/08/28-06:01:39.160626 668                       Options.max_log_file_size: 1048576
2025/08/28-06:01:39.160628 668                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:01:39.160633 668                   Options.log_file_time_to_roll: 0
2025/08/28-06:01:39.160639 668                       Options.keep_log_file_num: 1
2025/08/28-06:01:39.160650 668                    Options.recycle_log_file_num: 0
2025/08/28-06:01:39.160653 668                         Options.allow_fallocate: 1
2025/08/28-06:01:39.160658 668                        Options.allow_mmap_reads: 0
2025/08/28-06:01:39.160660 668                       Options.allow_mmap_writes: 0
2025/08/28-06:01:39.160666 668                        Options.use_direct_reads: 0
2025/08/28-06:01:39.160669 668                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:01:39.160671 668          Options.create_missing_column_families: 1
2025/08/28-06:01:39.160675 668                              Options.db_log_dir: 
2025/08/28-06:01:39.160677 668                                 Options.wal_dir: 
2025/08/28-06:01:39.160680 668                Options.table_cache_numshardbits: 6
2025/08/28-06:01:39.160682 668                         Options.WAL_ttl_seconds: 0
2025/08/28-06:01:39.160686 668                       Options.WAL_size_limit_MB: 0
2025/08/28-06:01:39.160688 668                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:01:39.160690 668             Options.manifest_preallocation_size: 4194304
2025/08/28-06:01:39.160693 668                     Options.is_fd_close_on_exec: 1
2025/08/28-06:01:39.160695 668                   Options.advise_random_on_open: 1
2025/08/28-06:01:39.160727 668                    Options.db_write_buffer_size: 0
2025/08/28-06:01:39.160749 668                    Options.write_buffer_manager: 0x7f1f2f6013c0
2025/08/28-06:01:39.160754 668           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:01:39.160769 668                      Options.use_adaptive_mutex: 0
2025/08/28-06:01:39.160772 668                            Options.rate_limiter: (nil)
2025/08/28-06:01:39.160776 668     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:01:39.160778 668                       Options.wal_recovery_mode: 0
2025/08/28-06:01:39.160780 668                  Options.enable_thread_tracking: 0
2025/08/28-06:01:39.160788 668                  Options.enable_pipelined_write: 0
2025/08/28-06:01:39.160790 668                  Options.unordered_write: 0
2025/08/28-06:01:39.160798 668         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:01:39.160800 668      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:01:39.160805 668             Options.write_thread_max_yield_usec: 100
2025/08/28-06:01:39.160814 668            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:01:39.160817 668                               Options.row_cache: None
2025/08/28-06:01:39.160822 668                              Options.wal_filter: None
2025/08/28-06:01:39.160828 668             Options.avoid_flush_during_recovery: 0
2025/08/28-06:01:39.160830 668             Options.allow_ingest_behind: 0
2025/08/28-06:01:39.160832 668             Options.two_write_queues: 0
2025/08/28-06:01:39.160834 668             Options.manual_wal_flush: 0
2025/08/28-06:01:39.160838 668             Options.wal_compression: 0
2025/08/28-06:01:39.160842 668             Options.background_close_inactive_wals: 0
2025/08/28-06:01:39.160844 668             Options.atomic_flush: 0
2025/08/28-06:01:39.160846 668             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:01:39.160853 668             Options.prefix_seek_opt_in_only: 0
2025/08/28-06:01:39.160855 668                 Options.persist_stats_to_disk: 0
2025/08/28-06:01:39.160867 668                 Options.write_dbid_to_manifest: 1
2025/08/28-06:01:39.160871 668                 Options.write_identity_file: 1
2025/08/28-06:01:39.160873 668                 Options.log_readahead_size: 0
2025/08/28-06:01:39.160877 668                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:01:39.160880 668                 Options.best_efforts_recovery: 0
2025/08/28-06:01:39.160883 668                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:01:39.160886 668            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:01:39.160889 668             Options.allow_data_in_errors: 0
2025/08/28-06:01:39.160893 668             Options.db_host_id: __hostname__
2025/08/28-06:01:39.160898 668             Options.enforce_single_del_contracts: true
2025/08/28-06:01:39.160907 668             Options.metadata_write_temperature: kUnknown
2025/08/28-06:01:39.160917 668             Options.wal_write_temperature: kUnknown
2025/08/28-06:01:39.160924 668             Options.max_background_jobs: 2
2025/08/28-06:01:39.160927 668             Options.max_background_compactions: -1
2025/08/28-06:01:39.160929 668             Options.max_subcompactions: 1
2025/08/28-06:01:39.160931 668             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:01:39.160950 668           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:01:39.160953 668             Options.delayed_write_rate : 16777216
2025/08/28-06:01:39.160955 668             Options.max_total_wal_size: 0
2025/08/28-06:01:39.160977 668             Options.delete_obsolete_files_period_micros: 180000000
2025/08/28-06:01:39.160980 668                   Options.stats_dump_period_sec: 600
2025/08/28-06:01:39.160982 668                 Options.stats_persist_period_sec: 600
2025/08/28-06:01:39.160984 668                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:01:39.160986 668                          Options.max_open_files: 256
2025/08/28-06:01:39.160988 668                          Options.bytes_per_sync: 0
2025/08/28-06:01:39.160991 668                      Options.wal_bytes_per_sync: 0
2025/08/28-06:01:39.160993 668                   Options.strict_bytes_per_sync: 0
2025/08/28-06:01:39.160995 668       Options.compaction_readahead_size: 2097152
2025/08/28-06:01:39.160997 668                  Options.max_background_flushes: -1
2025/08/28-06:01:39.160999 668 Options.daily_offpeak_time_utc: 
2025/08/28-06:01:39.161003 668 Compression algorithms supported:
2025/08/28-06:01:39.161006 668 	kZSTD supported: 0
2025/08/28-06:01:39.161065 668 	kXpressCompression supported: 0
2025/08/28-06:01:39.161068 668 	kBZip2Compression supported: 0
2025/08/28-06:01:39.161074 668 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:01:39.161084 668 	kLZ4Compression supported: 1
2025/08/28-06:01:39.161086 668 	kZlibCompression supported: 0
2025/08/28-06:01:39.161089 668 	kLZ4HCCompression supported: 1
2025/08/28-06:01:39.161092 668 	kSnappyCompression supported: 1
2025/08/28-06:01:39.161102 668 Fast CRC32 supported: Not supported on x86
2025/08/28-06:01:39.161105 668 DMutex implementation: pthread_mutex_t
2025/08/28-06:01:39.161166 668 Jemalloc supported: 0
2025/08/28-06:01:39.168084 668               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.168102 668           Options.merge_operator: None
2025/08/28-06:01:39.168107 668        Options.compaction_filter: None
2025/08/28-06:01:39.168112 668        Options.compaction_filter_factory: None
2025/08/28-06:01:39.168116 668  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.168119 668         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.168122 668            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.168287 668            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f2f600160)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f2f60b250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.168305 668        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.168383 668  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.168398 668          Options.compression: LZ4
2025/08/28-06:01:39.168404 668                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.168409 668       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.168446 668   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.168451 668             Options.num_levels: 7
2025/08/28-06:01:39.168455 668        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.168460 668     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.168464 668     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.168470 668            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.168479 668                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.168483 668               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.168488 668         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.168491 668         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.168495 668         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.168500 668                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.168504 668         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.168508 668         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.168512 668            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.168518 668                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.168530 668               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.168534 668         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.168539 668         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.168543 668         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.168546 668         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.168554 668                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.168558 668         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.168561 668      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.168566 668          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.168570 668              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.168577 668                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.168580 668             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.168585 668                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.168589 668 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.168598 668          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.168605 668 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.168609 668 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.168613 668 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.168617 668 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.168621 668 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.168625 668 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.168629 668 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.168642 668       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.168649 668                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.168654 668                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.168658 668   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.168662 668   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.168667 668                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.168673 668                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.168684 668                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.168688 668 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.168700 668 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.168726 668 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.168734 668 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.168739 668 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.168744 668 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.168751 668 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.168755 668 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.168764 668 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.168919 668                   Options.table_properties_collectors: 
2025/08/28-06:01:39.168925 668                   Options.inplace_update_support: 0
2025/08/28-06:01:39.168929 668                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.168934 668               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.168939 668               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.168943 668   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.168948 668                           Options.bloom_locality: 0
2025/08/28-06:01:39.168953 668                    Options.max_successive_merges: 0
2025/08/28-06:01:39.168962 668             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.168966 668                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.168971 668                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.168975 668                Options.force_consistency_checks: 1
2025/08/28-06:01:39.168979 668                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.168983 668                               Options.ttl: 2592000
2025/08/28-06:01:39.168993 668          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.169000 668                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.169004 668  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.169008 668    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.169023 668                       Options.enable_blob_files: false
2025/08/28-06:01:39.169027 668                           Options.min_blob_size: 0
2025/08/28-06:01:39.169061 668                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.169065 668                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.169069 668          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.169075 668      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.169080 668 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.169084 668          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.169088 668                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.169092 668         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.169156 668            Options.memtable_max_range_deletions: 0
2025/08/28-06:01:39.193486 668 DB pointer 0x7f1f2f67e000
2025/08/28-06:01:39.490808 33               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.490818 33           Options.merge_operator: None
2025/08/28-06:01:39.490821 33        Options.compaction_filter: None
2025/08/28-06:01:39.490823 33        Options.compaction_filter_factory: None
2025/08/28-06:01:39.490825 33  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.490828 33         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.490830 33            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.490863 33            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f41a00dc0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f41ab8010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.490871 33        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.490874 33  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.490877 33          Options.compression: LZ4
2025/08/28-06:01:39.490879 33                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.490881 33       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.490883 33   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.490885 33             Options.num_levels: 7
2025/08/28-06:01:39.490887 33        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.490890 33     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.490892 33     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.490894 33            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.490896 33                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.490898 33               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.490900 33         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.490902 33         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.490905 33         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.490907 33                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.490909 33         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.490911 33         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.490913 33            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.490915 33                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.490917 33               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.490919 33         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.490921 33         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.490923 33         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.490925 33         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.490930 33                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.490932 33         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.490934 33      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.490936 33          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.490938 33              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.490940 33                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.490942 33             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.490944 33                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.490946 33 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.490950 33          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.490953 33 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.490956 33 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.490958 33 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.490960 33 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.490962 33 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.490964 33 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.490966 33 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.490968 33       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.490970 33                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.490973 33                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.490975 33   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.490977 33   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.490979 33                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.490982 33                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.490985 33                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.490987 33 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.490989 33 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.490991 33 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.490993 33 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.490996 33 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.490998 33 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.491000 33 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.491002 33 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.491004 33 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.491010 33                   Options.table_properties_collectors: 
2025/08/28-06:01:39.491012 33                   Options.inplace_update_support: 0
2025/08/28-06:01:39.491015 33                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.491017 33               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.491020 33               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.491022 33   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.491024 33                           Options.bloom_locality: 0
2025/08/28-06:01:39.491026 33                    Options.max_successive_merges: 0
2025/08/28-06:01:39.491028 33             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.491030 33                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.491033 33                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.491035 33                Options.force_consistency_checks: 1
2025/08/28-06:01:39.491037 33                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.491039 33                               Options.ttl: 2592000
2025/08/28-06:01:39.491041 33          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.491046 33                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.491048 33  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.491051 33    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.491053 33                       Options.enable_blob_files: false
2025/08/28-06:01:39.491055 33                           Options.min_blob_size: 0
2025/08/28-06:01:39.491057 33                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.491059 33                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.491061 33          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.491064 33      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.491067 33 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.491069 33          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.491071 33                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.491074 33         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.491076 33            Options.memtable_max_range_deletions: 0
