2025/08/28-06:01:39.160429 669 RocksDB version: 9.9.3
2025/08/28-06:01:39.160483 669 Compile date 2024-12-05 01:25:31
2025/08/28-06:01:39.160489 669 DB SUMMARY
2025/08/28-06:01:39.160495 669 Host name (Env):  adbfaf2ea9d4
2025/08/28-06:01:39.160498 669 DB Session ID:  6DXSETNP6YHOH7T8I5WX
2025/08/28-06:01:39.160567 669 SST files in ./storage/collections/jina_embeddings_articles/1/segments/95be03ef-9117-4b26-a4d1-cd2d2fac9058/payload_index dir, Total Num: 0, files: 
2025/08/28-06:01:39.160588 669 Write Ahead Log file in ./storage/collections/jina_embeddings_articles/1/segments/95be03ef-9117-4b26-a4d1-cd2d2fac9058/payload_index: 
2025/08/28-06:01:39.160593 669                         Options.error_if_exists: 0
2025/08/28-06:01:39.160596 669                       Options.create_if_missing: 1
2025/08/28-06:01:39.160599 669                         Options.paranoid_checks: 1
2025/08/28-06:01:39.160607 669             Options.flush_verify_memtable_count: 1
2025/08/28-06:01:39.160610 669          Options.compaction_verify_record_count: 1
2025/08/28-06:01:39.160613 669                               Options.track_and_verify_wals_in_manifest: 0
2025/08/28-06:01:39.160621 669        Options.verify_sst_unique_id_in_manifest: 1
2025/08/28-06:01:39.160625 669                                     Options.env: 0x7f1f2f230460
2025/08/28-06:01:39.160628 669                                      Options.fs: PosixFileSystem
2025/08/28-06:01:39.160638 669                                Options.info_log: 0x7f1f2f298000
2025/08/28-06:01:39.160647 669                Options.max_file_opening_threads: 16
2025/08/28-06:01:39.160651 669                              Options.statistics: (nil)
2025/08/28-06:01:39.160654 669                               Options.use_fsync: 0
2025/08/28-06:01:39.160657 669                       Options.max_log_file_size: 1048576
2025/08/28-06:01:39.160660 669                  Options.max_manifest_file_size: 1073741824
2025/08/28-06:01:39.161023 669                   Options.log_file_time_to_roll: 0
2025/08/28-06:01:39.161030 669                       Options.keep_log_file_num: 1
2025/08/28-06:01:39.161034 669                    Options.recycle_log_file_num: 0
2025/08/28-06:01:39.161036 669                         Options.allow_fallocate: 1
2025/08/28-06:01:39.161040 669                        Options.allow_mmap_reads: 0
2025/08/28-06:01:39.161043 669                       Options.allow_mmap_writes: 0
2025/08/28-06:01:39.161045 669                        Options.use_direct_reads: 0
2025/08/28-06:01:39.161048 669                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/28-06:01:39.161051 669          Options.create_missing_column_families: 1
2025/08/28-06:01:39.161054 669                              Options.db_log_dir: 
2025/08/28-06:01:39.161119 669                                 Options.wal_dir: 
2025/08/28-06:01:39.161124 669                Options.table_cache_numshardbits: 6
2025/08/28-06:01:39.161127 669                         Options.WAL_ttl_seconds: 0
2025/08/28-06:01:39.161130 669                       Options.WAL_size_limit_MB: 0
2025/08/28-06:01:39.161134 669                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/28-06:01:39.161137 669             Options.manifest_preallocation_size: 4194304
2025/08/28-06:01:39.161140 669                     Options.is_fd_close_on_exec: 1
2025/08/28-06:01:39.161143 669                   Options.advise_random_on_open: 1
2025/08/28-06:01:39.161150 669                    Options.db_write_buffer_size: 0
2025/08/28-06:01:39.161153 669                    Options.write_buffer_manager: 0x7f1f2f201460
2025/08/28-06:01:39.161156 669           Options.random_access_max_buffer_size: 1048576
2025/08/28-06:01:39.161159 669                      Options.use_adaptive_mutex: 0
2025/08/28-06:01:39.161162 669                            Options.rate_limiter: (nil)
2025/08/28-06:01:39.161165 669     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/28-06:01:39.161168 669                       Options.wal_recovery_mode: 0
2025/08/28-06:01:39.161173 669                  Options.enable_thread_tracking: 0
2025/08/28-06:01:39.161180 669                  Options.enable_pipelined_write: 0
2025/08/28-06:01:39.161183 669                  Options.unordered_write: 0
2025/08/28-06:01:39.161187 669         Options.allow_concurrent_memtable_write: 1
2025/08/28-06:01:39.161193 669      Options.enable_write_thread_adaptive_yield: 1
2025/08/28-06:01:39.161196 669             Options.write_thread_max_yield_usec: 100
2025/08/28-06:01:39.161202 669            Options.write_thread_slow_yield_usec: 3
2025/08/28-06:01:39.161207 669                               Options.row_cache: None
2025/08/28-06:01:39.161212 669                              Options.wal_filter: None
2025/08/28-06:01:39.161215 669             Options.avoid_flush_during_recovery: 0
2025/08/28-06:01:39.161218 669             Options.allow_ingest_behind: 0
2025/08/28-06:01:39.161222 669             Options.two_write_queues: 0
2025/08/28-06:01:39.161225 669             Options.manual_wal_flush: 0
2025/08/28-06:01:39.161228 669             Options.wal_compression: 0
2025/08/28-06:01:39.161232 669             Options.background_close_inactive_wals: 0
2025/08/28-06:01:39.161237 669             Options.atomic_flush: 0
2025/08/28-06:01:39.161239 669             Options.avoid_unnecessary_blocking_io: 0
2025/08/28-06:01:39.161241 669             Options.prefix_seek_opt_in_only: 0
2025/08/28-06:01:39.161243 669                 Options.persist_stats_to_disk: 0
2025/08/28-06:01:39.161247 669                 Options.write_dbid_to_manifest: 1
2025/08/28-06:01:39.161249 669                 Options.write_identity_file: 1
2025/08/28-06:01:39.161252 669                 Options.log_readahead_size: 0
2025/08/28-06:01:39.161254 669                 Options.file_checksum_gen_factory: Unknown
2025/08/28-06:01:39.161256 669                 Options.best_efforts_recovery: 0
2025/08/28-06:01:39.161258 669                Options.max_bgerror_resume_count: 2147483647
2025/08/28-06:01:39.161262 669            Options.bgerror_resume_retry_interval: 1000000
2025/08/28-06:01:39.161264 669             Options.allow_data_in_errors: 0
2025/08/28-06:01:39.161266 669             Options.db_host_id: __hostname__
2025/08/28-06:01:39.161268 669             Options.enforce_single_del_contracts: true
2025/08/28-06:01:39.161270 669             Options.metadata_write_temperature: kUnknown
2025/08/28-06:01:39.161273 669             Options.wal_write_temperature: kUnknown
2025/08/28-06:01:39.161275 669             Options.max_background_jobs: 2
2025/08/28-06:01:39.161277 669             Options.max_background_compactions: -1
2025/08/28-06:01:39.161279 669             Options.max_subcompactions: 1
2025/08/28-06:01:39.161285 669             Options.avoid_flush_during_shutdown: 0
2025/08/28-06:01:39.161288 669           Options.writable_file_max_buffer_size: 1048576
2025/08/28-06:01:39.161291 669             Options.delayed_write_rate : 16777216
2025/08/28-06:01:39.161294 669             Options.max_total_wal_size: 0
2025/08/28-06:01:39.161296 669             Options.delete_obsolete_files_period_micros: 180000000
2025/08/28-06:01:39.161298 669                   Options.stats_dump_period_sec: 600
2025/08/28-06:01:39.161301 669                 Options.stats_persist_period_sec: 600
2025/08/28-06:01:39.161304 669                 Options.stats_history_buffer_size: 1048576
2025/08/28-06:01:39.161307 669                          Options.max_open_files: 256
2025/08/28-06:01:39.161310 669                          Options.bytes_per_sync: 0
2025/08/28-06:01:39.161312 669                      Options.wal_bytes_per_sync: 0
2025/08/28-06:01:39.161314 669                   Options.strict_bytes_per_sync: 0
2025/08/28-06:01:39.161317 669       Options.compaction_readahead_size: 2097152
2025/08/28-06:01:39.161322 669                  Options.max_background_flushes: -1
2025/08/28-06:01:39.161324 669 Options.daily_offpeak_time_utc: 
2025/08/28-06:01:39.161327 669 Compression algorithms supported:
2025/08/28-06:01:39.161330 669 	kZSTD supported: 0
2025/08/28-06:01:39.161333 669 	kXpressCompression supported: 0
2025/08/28-06:01:39.161335 669 	kBZip2Compression supported: 0
2025/08/28-06:01:39.161343 669 	kZSTDNotFinalCompression supported: 0
2025/08/28-06:01:39.161346 669 	kLZ4Compression supported: 1
2025/08/28-06:01:39.161350 669 	kZlibCompression supported: 0
2025/08/28-06:01:39.161354 669 	kLZ4HCCompression supported: 1
2025/08/28-06:01:39.161356 669 	kSnappyCompression supported: 1
2025/08/28-06:01:39.161359 669 Fast CRC32 supported: Not supported on x86
2025/08/28-06:01:39.161362 669 DMutex implementation: pthread_mutex_t
2025/08/28-06:01:39.161365 669 Jemalloc supported: 0
2025/08/28-06:01:39.167984 669               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.167999 669           Options.merge_operator: None
2025/08/28-06:01:39.168003 669        Options.compaction_filter: None
2025/08/28-06:01:39.168029 669        Options.compaction_filter_factory: None
2025/08/28-06:01:39.168034 669  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.168044 669         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.168112 669            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.168385 669            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f2f200160)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f2f209250
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.168402 669        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.168407 669  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.168414 669          Options.compression: LZ4
2025/08/28-06:01:39.168418 669                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.168422 669       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.168427 669   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.168435 669             Options.num_levels: 7
2025/08/28-06:01:39.168445 669        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.168450 669     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.168457 669     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.168461 669            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.168468 669                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.168481 669               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.168486 669         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.168491 669         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.168496 669         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.168500 669                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.168506 669         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.168510 669         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.168517 669            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.168521 669                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.168531 669               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.168536 669         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.168540 669         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.168545 669         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.168549 669         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.168553 669                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.168560 669         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.168565 669      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.168569 669          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.168573 669              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.168578 669                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.168582 669             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.168586 669                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.168591 669 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.168601 669          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.168609 669 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.168614 669 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.168618 669 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.168624 669 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.168628 669 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.168633 669 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.168639 669 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.168647 669       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.168653 669                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.168657 669                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.168661 669   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.168665 669   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.168673 669                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.168680 669                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.168690 669                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.168728 669 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.168732 669 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.168738 669 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.168742 669 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.168747 669 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.168752 669 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.168757 669 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.168761 669 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.168767 669 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.168919 669                   Options.table_properties_collectors: 
2025/08/28-06:01:39.168926 669                   Options.inplace_update_support: 0
2025/08/28-06:01:39.168930 669                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.168935 669               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.168940 669               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.168945 669   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.168948 669                           Options.bloom_locality: 0
2025/08/28-06:01:39.168955 669                    Options.max_successive_merges: 0
2025/08/28-06:01:39.168965 669             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.168972 669                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.168976 669                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.168983 669                Options.force_consistency_checks: 1
2025/08/28-06:01:39.168989 669                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.169006 669                               Options.ttl: 2592000
2025/08/28-06:01:39.169011 669          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.169031 669                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.169067 669  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.169071 669    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.169075 669                       Options.enable_blob_files: false
2025/08/28-06:01:39.169091 669                           Options.min_blob_size: 0
2025/08/28-06:01:39.169116 669                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.169124 669                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.169128 669          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.169140 669      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.169158 669 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.169177 669          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.169181 669                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.169186 669         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.169191 669            Options.memtable_max_range_deletions: 0
2025/08/28-06:01:39.196808 669 DB pointer 0x7f1f2f284000
2025/08/28-06:01:39.503641 33               Options.comparator: leveldb.BytewiseComparator
2025/08/28-06:01:39.503645 33           Options.merge_operator: None
2025/08/28-06:01:39.503646 33        Options.compaction_filter: None
2025/08/28-06:01:39.503647 33        Options.compaction_filter_factory: None
2025/08/28-06:01:39.503648 33  Options.sst_partitioner_factory: None
2025/08/28-06:01:39.503648 33         Options.memtable_factory: SkipListFactory
2025/08/28-06:01:39.503649 33            Options.table_factory: BlockBasedTable
2025/08/28-06:01:39.503681 33            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f1f41a00ae0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f1f41ab8310
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/08/28-06:01:39.503684 33        Options.write_buffer_size: 10485760
2025/08/28-06:01:39.503684 33  Options.max_write_buffer_number: 2
2025/08/28-06:01:39.503686 33          Options.compression: LZ4
2025/08/28-06:01:39.503687 33                  Options.bottommost_compression: Disabled
2025/08/28-06:01:39.503688 33       Options.prefix_extractor: nullptr
2025/08/28-06:01:39.503689 33   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/28-06:01:39.503690 33             Options.num_levels: 7
2025/08/28-06:01:39.503691 33        Options.min_write_buffer_number_to_merge: 1
2025/08/28-06:01:39.503692 33     Options.max_write_buffer_number_to_maintain: 0
2025/08/28-06:01:39.503694 33     Options.max_write_buffer_size_to_maintain: 0
2025/08/28-06:01:39.503695 33            Options.bottommost_compression_opts.window_bits: -14
2025/08/28-06:01:39.503696 33                  Options.bottommost_compression_opts.level: 32767
2025/08/28-06:01:39.503697 33               Options.bottommost_compression_opts.strategy: 0
2025/08/28-06:01:39.503698 33         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.503699 33         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.503706 33         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/28-06:01:39.503706 33                  Options.bottommost_compression_opts.enabled: false
2025/08/28-06:01:39.503707 33         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.503708 33         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.503709 33            Options.compression_opts.window_bits: -14
2025/08/28-06:01:39.503709 33                  Options.compression_opts.level: 32767
2025/08/28-06:01:39.503710 33               Options.compression_opts.strategy: 0
2025/08/28-06:01:39.503711 33         Options.compression_opts.max_dict_bytes: 0
2025/08/28-06:01:39.503711 33         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/28-06:01:39.503712 33         Options.compression_opts.use_zstd_dict_trainer: true
2025/08/28-06:01:39.503713 33         Options.compression_opts.parallel_threads: 1
2025/08/28-06:01:39.503714 33                  Options.compression_opts.enabled: false
2025/08/28-06:01:39.503715 33         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/28-06:01:39.503716 33      Options.level0_file_num_compaction_trigger: 4
2025/08/28-06:01:39.503717 33          Options.level0_slowdown_writes_trigger: 20
2025/08/28-06:01:39.503719 33              Options.level0_stop_writes_trigger: 36
2025/08/28-06:01:39.503720 33                   Options.target_file_size_base: 67108864
2025/08/28-06:01:39.503721 33             Options.target_file_size_multiplier: 1
2025/08/28-06:01:39.503721 33                Options.max_bytes_for_level_base: 268435456
2025/08/28-06:01:39.503722 33 Options.level_compaction_dynamic_level_bytes: 1
2025/08/28-06:01:39.503723 33          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/28-06:01:39.503724 33 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/28-06:01:39.503725 33 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/28-06:01:39.503726 33 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/28-06:01:39.503726 33 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/28-06:01:39.503727 33 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/28-06:01:39.503727 33 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/28-06:01:39.503728 33 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/28-06:01:39.503729 33       Options.max_sequential_skip_in_iterations: 8
2025/08/28-06:01:39.503730 33                    Options.max_compaction_bytes: 1677721600
2025/08/28-06:01:39.503731 33                        Options.arena_block_size: 1048576
2025/08/28-06:01:39.503731 33   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/28-06:01:39.503732 33   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/28-06:01:39.503733 33                Options.disable_auto_compactions: 0
2025/08/28-06:01:39.503734 33                        Options.compaction_style: kCompactionStyleLevel
2025/08/28-06:01:39.503735 33                          Options.compaction_pri: kMinOverlappingRatio
2025/08/28-06:01:39.503735 33 Options.compaction_options_universal.size_ratio: 1
2025/08/28-06:01:39.503736 33 Options.compaction_options_universal.min_merge_width: 2
2025/08/28-06:01:39.503737 33 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/28-06:01:39.503738 33 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/28-06:01:39.503738 33 Options.compaction_options_universal.compression_size_percent: -1
2025/08/28-06:01:39.503739 33 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/28-06:01:39.503740 33 Options.compaction_options_universal.max_read_amp: -1
2025/08/28-06:01:39.503741 33 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/28-06:01:39.503741 33 Options.compaction_options_fifo.allow_compaction: 0
2025/08/28-06:01:39.503744 33                   Options.table_properties_collectors: 
2025/08/28-06:01:39.503745 33                   Options.inplace_update_support: 0
2025/08/28-06:01:39.503745 33                 Options.inplace_update_num_locks: 10000
2025/08/28-06:01:39.503746 33               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/28-06:01:39.503747 33               Options.memtable_whole_key_filtering: 0
2025/08/28-06:01:39.503748 33   Options.memtable_huge_page_size: 0
2025/08/28-06:01:39.503749 33                           Options.bloom_locality: 0
2025/08/28-06:01:39.503749 33                    Options.max_successive_merges: 0
2025/08/28-06:01:39.503750 33             Options.strict_max_successive_merges: 0
2025/08/28-06:01:39.503751 33                Options.optimize_filters_for_hits: 0
2025/08/28-06:01:39.503751 33                Options.paranoid_file_checks: 0
2025/08/28-06:01:39.503752 33                Options.force_consistency_checks: 1
2025/08/28-06:01:39.503753 33                Options.report_bg_io_stats: 0
2025/08/28-06:01:39.503753 33                               Options.ttl: 2592000
2025/08/28-06:01:39.503754 33          Options.periodic_compaction_seconds: 0
2025/08/28-06:01:39.503755 33                        Options.default_temperature: kUnknown
2025/08/28-06:01:39.503756 33  Options.preclude_last_level_data_seconds: 0
2025/08/28-06:01:39.503756 33    Options.preserve_internal_time_seconds: 0
2025/08/28-06:01:39.503757 33                       Options.enable_blob_files: false
2025/08/28-06:01:39.503758 33                           Options.min_blob_size: 0
2025/08/28-06:01:39.503758 33                          Options.blob_file_size: 268435456
2025/08/28-06:01:39.503759 33                   Options.blob_compression_type: NoCompression
2025/08/28-06:01:39.503760 33          Options.enable_blob_garbage_collection: false
2025/08/28-06:01:39.503761 33      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/28-06:01:39.503762 33 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/28-06:01:39.503763 33          Options.blob_compaction_readahead_size: 0
2025/08/28-06:01:39.503765 33                Options.blob_file_starting_level: 0
2025/08/28-06:01:39.503766 33         Options.experimental_mempurge_threshold: 0.000000
2025/08/28-06:01:39.503767 33            Options.memtable_max_range_deletions: 0
