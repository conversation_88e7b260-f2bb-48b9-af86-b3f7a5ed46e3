# Production Qdrant Configuration - Maximum Concurrent Throughput
log_level: INFO

# Production logging configuration
logger:
  format: json
  on_disk:
    enabled: true
    log_file: /qdrant/storage/qdrant.log
    log_level: WARN
    format: json

# Storage configuration optimized for maximum concurrent throughput
storage:
  # Where to store all the data
  storage_path: /qdrant/storage

  # Where to store snapshots
  snapshots_path: /qdrant/storage/snapshots

  snapshots_config:
    snapshots_storage: local

  # Where to store temporary files
  temp_path: /qdrant/storage/temp

  # Store payloads on disk to save RAM for vectors (production best practice)
  on_disk_payload: true

  # Remove all concurrency limits for maximum throughput
  update_concurrency: null

  # Write-ahead-log optimized for high-throughput batch operations
  wal:
    # Large WAL segments for better batch performance (production recommended)
    wal_capacity_mb: 256
    # Create segments ahead for high write throughput
    wal_segments_ahead: 5

  # Normal node configuration
  node_type: "Normal"

  # Performance configuration for maximum concurrent processing
  performance:
    # Use all available cores for search operations
    max_search_threads: 0

    # Remove optimization thread limits for maximum performance
    max_optimization_threads: 0

    # Use all available CPUs for optimization
    optimizer_cpu_budget: 0

    # Remove rate limiting for maximum throughput
    update_rate_limit: null

    # Allow unlimited concurrent shard transfers
    incoming_shard_transfers_limit: null
    outgoing_shard_transfers_limit: null

    # Enable async scorer for better performance on Linux
    async_scorer: true

  # Optimizer configuration for maximum concurrent throughput
  optimizers:
    # Aggressive optimization threshold for better performance
    deleted_threshold: 0.1

    # Lower minimum for more frequent optimization
    vacuum_min_vector_number: 500

    # Fewer segments for better throughput (production recommended: 2-4)
    default_segment_number: 2

    # Large segments for better batch performance
    max_segment_size_kb: 5000000

    # Higher threshold for memory mapping (keep more in RAM)
    memmap_threshold_kb: 200000

    # High indexing threshold for better batch insert performance
    indexing_threshold_kb: 100000

    # Frequent flushes for better consistency
    flush_interval_sec: 2

    # Remove optimization thread limits
    max_optimization_threads: null

  # HNSW Index configuration optimized for production throughput
  hnsw_index:
    # Balanced M for good performance and memory usage
    m: 16

    # Higher ef_construct for better index quality
    ef_construct: 200

    # Higher threshold for full scan vs HNSW
    full_scan_threshold_kb: 20000

    # Use all available threads for index building
    max_indexing_threads: 0

    # Keep HNSW index in memory for better performance
    on_disk: false

    # Custom M for payload index
    payload_m: 16

  # Collection configuration for high availability and performance
  collection:
    # Replication factor for production (2 for high availability)
    replication_factor: 2

    # Write consistency for production
    write_consistency_factor: 1

    # Default vector configuration
    vectors:
      # Keep vectors in memory for maximum performance
      on_disk: false

    # No default quantization (configure per collection as needed)
    quantization: null

# Service configuration optimized for maximum concurrent throughput
service:
  # Very large request size for massive batch operations
  max_request_size_mb: 1024

  # Use all available cores for serving API
  max_workers: 0

  # Bind to all interfaces
  host: 0.0.0.0

  # Standard ports
  http_port: 6333
  grpc_port: 6334

  # Enable CORS for web applications
  enable_cors: true

  # Disable TLS for internal cluster communication (enable for external production access)
  enable_tls: false

  # Disable client certificate verification
  verify_https_client_certificate: false

  # Enable hardware reporting for monitoring
  hardware_reporting: true

# Cluster configuration for distributed deployment
cluster:
  # Enable clustering
  enabled: true

  # P2P configuration
  p2p:
    # Port for internal communication between peers
    port: 6335

    # Disable TLS for internal communication (enable for production with external access)
    enable_tls: false

  # Consensus configuration optimized for high throughput
  consensus:
    # Optimized tick period for production cluster performance
    tick_period_ms: 100

# Disable telemetry for production
telemetry_disabled: true
