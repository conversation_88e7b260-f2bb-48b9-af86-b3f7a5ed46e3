# Production Qdrant Configuration - Distributed Multi-Node with Sharding
log_level: INFO

# Storage configuration optimized for distributed deployment
storage:
  # Where to store all the data
  storage_path: /qdrant/storage

  # Where to store snapshots
  snapshots_path: /qdrant/storage/snapshots

  # Where to store temporary files
  temp_path: /qdrant/storage/temp

  # Store payloads on disk to save RAM for vectors
  on_disk_payload: true

  # Remove concurrency limits for maximum throughput
  update_concurrency: null

  # Write-ahead-log optimized for high-throughput batch operations
  wal:
    # Large WAL segments for better batch performance
    wal_capacity_mb: 256
    # Create segments ahead for high write throughput
    wal_segments_ahead: 5

  # Performance configuration for maximum concurrent processing
  performance:
    # Use all available cores for search operations
    max_search_threads: 0

    # Remove optimization thread limits for maximum performance
    max_optimization_threads: 0

    # Use all available CPUs for optimization
    optimizer_cpu_budget: 0

    # Remove rate limiting for maximum throughput
    update_rate_limit: null

    # Allow unlimited concurrent shard transfers for distributed setup
    incoming_shard_transfers_limit: null
    outgoing_shard_transfers_limit: null

    # Enable async scorer for better performance
    async_scorer: true

  # Optimizer configuration for distributed high-throughput
  optimizers:
    # Aggressive optimization for better performance
    deleted_threshold: 0.1

    # Lower minimum for more frequent optimization
    vacuum_min_vector_number: 500

    # Optimal segments for distributed setup
    default_segment_number: 3

    # Large segments for better batch performance
    max_segment_size_kb: 5000000

    # Higher threshold for memory mapping
    memmap_threshold_kb: 200000

    # High indexing threshold for better batch insert performance
    indexing_threshold_kb: 100000

    # Frequent flushes for better consistency in distributed setup
    flush_interval_sec: 2

    # Remove optimization thread limits
    max_optimization_threads: null

  # HNSW Index configuration optimized for distributed deployment
  hnsw_index:
    # Balanced M for good performance and memory usage
    m: 16

    # Higher ef_construct for better index quality
    ef_construct: 200

    # Higher threshold for full scan vs HNSW
    full_scan_threshold_kb: 20000

    # Use all available threads for index building
    max_indexing_threads: 0

    # Keep HNSW index in memory for better performance
    on_disk: false

    # Custom M for payload index
    payload_m: 16

  # Collection configuration for distributed deployment with sharding
  collection:
    # Replication factor for distributed setup (3 nodes = 2 replicas)
    replication_factor: 2

    # Write consistency for distributed setup
    write_consistency_factor: 1

    # Sharding configuration for distributed deployment
    shard_number: 3  # 3 shards across 3 nodes

    # Default vector configuration
    vectors:
      # Keep vectors in memory for maximum performance
      on_disk: false

    # No default quantization
    quantization: null

# Service configuration optimized for distributed deployment
service:
  # Large request size for batch operations
  max_request_size_mb: 1024

  # Use all available cores for serving API
  max_workers: 0

  # Bind to all interfaces
  host: 0.0.0.0

  # Standard ports
  http_port: 6333
  grpc_port: 6334

  # Enable CORS for web applications
  enable_cors: true

  # Disable TLS for internal cluster communication
  enable_tls: false

  # Disable client certificate verification
  verify_https_client_certificate: false

# Cluster configuration for distributed deployment with sharding
cluster:
  # Enable clustering
  enabled: true

  # P2P configuration for distributed nodes
  p2p:
    # Port for internal communication between peers
    port: 6335

    # Disable TLS for internal communication
    enable_tls: false

  # Consensus configuration optimized for distributed performance
  consensus:
    # Slower tick period for better stability in distributed setup
    tick_period_ms: 200

    # Increase timeouts for better cluster stability
    bootstrap_timeout_sec: 30

# Disable telemetry
telemetry_disabled: true
