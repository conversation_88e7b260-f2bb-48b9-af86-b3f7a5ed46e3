# ULTRA HIGH-PERFORMANCE Qdrant Configuration - MAXIMUM CONCURRENT PROCESSING
log_level: WARN  # Reduce logging overhead

# Minimal logging for maximum performance
logger:
  format: text  # Faster than JSO<PERSON>
  on_disk:
    enabled: false  # Disable disk logging for max performance

# Storage configuration for MAXIMUM CONCURRENT THROUGHPUT
storage:
  # Where to store all the data
  storage_path: /qdrant/storage

  # Where to store snapshots
  snapshots_path: /qdrant/storage/snapshots

  snapshots_config:
    snapshots_storage: local

  # Where to store temporary files
  temp_path: /qdrant/storage/temp

  # Keep payloads in memory for maximum speed
  on_disk_payload: false

  # UNLIMITED concurrent updates - remove all bottlenecks
  update_concurrency: null

  # Write-ahead-log optimized for MAXIMUM throughput
  wal:
    # MASSIVE WAL segments for ultra-high batch performance
    wal_capacity_mb: 1024
    # Create many segments ahead for maximum write throughput
    wal_segments_ahead: 10

  # Normal node configuration
  node_type: "Normal"

  # EXTREME performance configuration - MAXIMUM CONCURRENT PROCESSING
  performance:
    # Use ALL available cores for search operations
    max_search_threads: 0

    # UNLIMITED optimization threads for maximum performance
    max_optimization_threads: 0

    # Use ALL available CPUs for optimization
    optimizer_cpu_budget: 0

    # REMOVE ALL RATE LIMITING - maximum throughput
    update_rate_limit: null

    # MAXIMUM concurrent shard transfers
    incoming_shard_transfers_limit: null
    outgoing_shard_transfers_limit: null

    # Enable async scorer for maximum performance (Linux only)
    async_scorer: true

  # EXTREME optimizer configuration for MAXIMUM CONCURRENT THROUGHPUT
  optimizers:
    # Very low threshold for aggressive optimization
    deleted_threshold: 0.05

    # Very low minimum for immediate optimization
    vacuum_min_vector_number: 100

    # SINGLE segment for maximum throughput (no segment overhead)
    default_segment_number: 1

    # MASSIVE segments for ultra-high batch performance
    max_segment_size_kb: 10000000

    # NEVER use memory mapping - keep everything in RAM
    memmap_threshold_kb: null

    # VERY HIGH indexing threshold - delay indexing for max insert speed
    indexing_threshold_kb: 200000

    # IMMEDIATE flushes for maximum write speed
    flush_interval_sec: 1

    # UNLIMITED optimization threads
    max_optimization_threads: null

  # HNSW Index optimized for MAXIMUM INSERTION SPEED
  hnsw_index:
    # Lower M for faster insertions (trade some accuracy for speed)
    m: 8

    # Lower ef_construct for faster index building
    ef_construct: 64

    # VERY HIGH threshold - prefer full scan over HNSW for small datasets
    full_scan_threshold_kb: 100000

    # Use ALL available threads for index building
    max_indexing_threads: 0

    # Keep HNSW index in memory for maximum performance
    on_disk: false

    # Lower M for payload index for speed
    payload_m: 8

  # Collection configuration for MAXIMUM CONCURRENT WRITES
  collection:
    # Single replica for maximum write speed (no replication overhead)
    replication_factor: 1

    # Single write consistency for maximum speed
    write_consistency_factor: 1

    # Default vector configuration for maximum speed
    vectors:
      # Keep ALL vectors in memory for maximum performance
      on_disk: false

    # No quantization for maximum insertion speed
    quantization: null

# Service configuration optimized for high throughput
service:
  # Larger request size for batch operations
  max_request_size_mb: 512

  # Use all available cores for serving API
  max_workers: 0

  # Bind to all interfaces
  host: 0.0.0.0

  # Standard ports
  http_port: 6333
  grpc_port: 6334

  # Enable CORS for web applications
  enable_cors: true

  # Disable TLS for internal cluster communication (enable for production with external access)
  enable_tls: false

  # Disable client certificate verification
  verify_https_client_certificate: false

# Cluster configuration for distributed deployment
cluster:
  # Enable clustering
  enabled: true

  # P2P configuration
  p2p:
    # Port for internal communication between peers
    port: 6335

    # Disable TLS for internal communication (enable for production)
    enable_tls: false

  # Consensus configuration optimized for performance
  consensus:
    # Faster tick period for better responsiveness
    tick_period_ms: 50

# Disable telemetry for production
telemetry_disabled: true
