global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Qdrant metrics
  - job_name: 'qdrant'
    static_configs:
      - targets: 
        - 'qdrant_node1:6333'
        - 'qdrant_node2:6333'
        - 'qdrant_node3:6333'
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Nginx metrics (if nginx-prometheus-exporter is added)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 15s
