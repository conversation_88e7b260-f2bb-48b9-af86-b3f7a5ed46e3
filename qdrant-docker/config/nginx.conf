events {
    worker_connections 1024;
}

http {
    # Upstream configuration for Qdrant HTTP API
    upstream qdrant_http {
        least_conn;
        server qdrant_node1:6333 max_fails=3 fail_timeout=30s;
        server qdrant_node2:6333 max_fails=3 fail_timeout=30s;
        server qdrant_node3:6333 max_fails=3 fail_timeout=30s;
    }

    # Health check configuration
    server {
        listen 6333;
        
        # Increase timeouts for large batch operations
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # Increase buffer sizes for large requests
        client_max_body_size 512m;
        proxy_buffering off;
        proxy_request_buffering off;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Proxy all requests to Qdrant cluster
        location / {
            proxy_pass http://qdrant_http;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Enable keepalive connections
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
    }
}

# Stream configuration for gRPC load balancing
stream {
    # Upstream configuration for Qdrant gRPC
    upstream qdrant_grpc {
        least_conn;
        server qdrant_node1:6334 max_fails=3 fail_timeout=30s;
        server qdrant_node2:6334 max_fails=3 fail_timeout=30s;
        server qdrant_node3:6334 max_fails=3 fail_timeout=30s;
    }

    # gRPC load balancer
    server {
        listen 6334;
        proxy_pass qdrant_grpc;
        proxy_timeout 300s;
        proxy_connect_timeout 30s;
    }
}
