#!/bin/bash

echo "🚀 Starting Simple Qdrant Cluster (1 Primary + 2 Secondary)..."

# Create necessary directories
echo "📁 Creating data directories..."
mkdir -p qdrant_data/primary qdrant_data/secondary1 qdrant_data/secondary2

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Start the cluster
echo "🔄 Starting Qdrant cluster..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 20

# Check cluster status
echo "🔍 Checking cluster status..."

echo "📊 Primary Node Status:"
curl -s http://localhost:6333/cluster 2>/dev/null | jq '.' || echo "Primary not ready yet"

echo "📊 Secondary 1 Status:"
curl -s http://localhost:6336/cluster 2>/dev/null | jq '.' || echo "Secondary 1 not ready yet"

echo "📊 Secondary 2 Status:"
curl -s http://localhost:6339/cluster 2>/dev/null | jq '.' || echo "Secondary 2 not ready yet"

echo ""
echo "✅ Cluster startup complete!"
echo ""
echo "🔗 Access Points:"
echo "   - Primary (HTTP): http://localhost:6333"
echo "   - Primary (gRPC): localhost:6334"
echo "   - Secondary 1 (HTTP): http://localhost:6336"
echo "   - Secondary 2 (HTTP): http://localhost:6339"
echo ""
echo "📝 To check logs: docker-compose logs -f [service_name]"
echo "📝 To stop cluster: docker-compose down"
