#!/bin/bash

echo "🚀 Starting Qdrant High-Performance Distributed Cluster..."

# Create necessary directories
echo "📁 Creating data directories..."
mkdir -p qdrant_data/node1 qdrant_data/node2 qdrant_data/node3

# Set proper permissions
chmod -R 755 qdrant_data/

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Remove old containers and volumes if they exist
echo "🧹 Cleaning up old containers..."
docker-compose rm -f

# Start the cluster
echo "🔄 Starting Qdrant cluster..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Check cluster status
echo "🔍 Checking cluster status..."

echo "📊 Node 1 Status:"
curl -s http://localhost:6340/cluster 2>/dev/null | jq '.' || echo "Node 1 not ready yet"

echo "📊 Node 2 Status:"
curl -s http://localhost:6343/cluster 2>/dev/null | jq '.' || echo "Node 2 not ready yet"

echo "📊 Node 3 Status:"
curl -s http://localhost:6346/cluster 2>/dev/null | jq '.' || echo "Node 3 not ready yet"

echo "🌐 Load Balancer Status:"
curl -s http://localhost:6333/cluster 2>/dev/null | jq '.' || echo "Load balancer not ready yet"

echo ""
echo "✅ Cluster startup complete!"
echo ""
echo "🔗 Access Points:"
echo "   - Load Balancer (HTTP): http://localhost:6333"
echo "   - Load Balancer (gRPC): localhost:6334"
echo "   - Node 1 (HTTP): http://localhost:6340"
echo "   - Node 2 (HTTP): http://localhost:6343"
echo "   - Node 3 (HTTP): http://localhost:6346"
echo "   - Prometheus: http://localhost:9090"
echo "   - Grafana: http://localhost:3000 (admin/admin)"
echo ""
echo "📝 To check logs: docker-compose logs -f [service_name]"
echo "📝 To stop cluster: docker-compose down"
